import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's notification preferences
    const { data: userData, error: userError } = await supabase
      .schema('tenants')
      .from('users')
      .select('notification_preferences')
      .eq('id', user.id)
      .single()

    if (userError) {
      console.error('Error fetching user preferences:', userError)
      return NextResponse.json({ error: 'Failed to fetch preferences' }, { status: 500 })
    }

    // Default preferences if none exist
    const defaultPreferences = {
      deadline_alerts: true,
      email_alerts: true,
      sms_alerts: false,
      return_alerts: true,
      morning_briefing: true,
      conflict_alerts: true,
      risk_alerts: true,
      frequency: 'immediate', // immediate, hourly, daily
      quiet_hours: {
        enabled: true,
        start: '22:00',
        end: '07:00'
      },
      channels: {
        in_app: true,
        email: true,
        sms: false,
        push: true
      },
      priorities: {
        critical: true,
        high: true,
        medium: false,
        low: false
      }
    }

    const preferences = userData?.notification_preferences || defaultPreferences

    return NextResponse.json({ preferences })

  } catch (error) {
    console.error('Error in notification preferences GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse the request body
    const body = await request.json()
    const { preferences } = body

    if (!preferences) {
      return NextResponse.json({ error: 'Preferences are required' }, { status: 400 })
    }

    // Update user's notification preferences
    const { error: updateError } = await supabase
      .schema('tenants')
      .from('users')
      .update({ 
        notification_preferences: preferences,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)

    if (updateError) {
      console.error('Error updating user preferences:', updateError)
      return NextResponse.json({ error: 'Failed to update preferences' }, { status: 500 })
    }

    // Log the preference change for security
    await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: 'user.preferences_updated',
        event_category: 'user_action',
        user_id: user.id,
        details: {
          preferences_updated: Object.keys(preferences),
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      })

    return NextResponse.json({ 
      success: true, 
      message: 'Notification preferences updated successfully' 
    })

  } catch (error) {
    console.error('Error in notification preferences PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse the request body
    const body = await request.json()
    const { action, notification_id } = body

    if (action === 'mark_read' && notification_id) {
      // Mark a specific notification as read
      const { error: markReadError } = await supabase
        .schema('tenants')
        .from('notifications')
        .update({ 
          read: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', notification_id)
        .eq('user_id', user.id)

      if (markReadError) {
        console.error('Error marking notification as read:', markReadError)
        return NextResponse.json({ error: 'Failed to mark notification as read' }, { status: 500 })
      }

      return NextResponse.json({ success: true })

    } else if (action === 'mark_all_read') {
      // Mark all notifications as read for the user
      const { error: markAllReadError } = await supabase
        .schema('tenants')
        .from('notifications')
        .update({ 
          read: true,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .eq('read', false)

      if (markAllReadError) {
        console.error('Error marking all notifications as read:', markAllReadError)
        return NextResponse.json({ error: 'Failed to mark all notifications as read' }, { status: 500 })
      }

      return NextResponse.json({ success: true })

    } else if (action === 'test_notification') {
      // Send a test notification
      const testNotification = {
        user_id: user.id,
        tenant_id: body.tenant_id,
        type: 'test',
        message: 'Test notification - your deadline alerts are working!',
        read: false,
        severity: 'medium',
        context: {
          test: true,
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      }

      const { error: testError } = await supabase
        .schema('tenants')
        .from('notifications')
        .insert(testNotification)

      if (testError) {
        console.error('Error sending test notification:', testError)
        return NextResponse.json({ error: 'Failed to send test notification' }, { status: 500 })
      }

      return NextResponse.json({ 
        success: true, 
        message: 'Test notification sent successfully' 
      })

    } else {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error in notification preferences POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

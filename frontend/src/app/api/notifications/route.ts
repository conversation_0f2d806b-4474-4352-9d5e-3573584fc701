import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const includeRead = searchParams.get('include_read') === 'true'
    const type = searchParams.get('type') // Filter by notification type
    const severity = searchParams.get('severity') // Filter by severity

    // Build query
    let query = supabase
      .schema('tenants')
      .from('notifications')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(limit)

    // Apply filters
    if (!includeRead) {
      query = query.eq('read', false)
    }

    if (type) {
      query = query.eq('type', type)
    }

    if (severity) {
      query = query.eq('severity', severity)
    }

    const { data: notifications, error: notificationsError } = await query

    if (notificationsError) {
      console.error('Error fetching notifications:', notificationsError)
      return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 })
    }

    // Get unread count
    const { count: unreadCount, error: countError } = await supabase
      .schema('tenants')
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('read', false)

    if (countError) {
      console.error('Error fetching unread count:', countError)
    }

    return NextResponse.json({
      notifications: notifications || [],
      unread_count: unreadCount || 0,
      total_count: notifications?.length || 0
    })

  } catch (error) {
    console.error('Error in notifications GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse the request body
    const body = await request.json()
    const { 
      title, 
      message, 
      type = 'general', 
      severity = 'medium',
      context = {},
      tenant_id 
    } = body

    if (!title || !message) {
      return NextResponse.json({ error: 'Title and message are required' }, { status: 400 })
    }

    // Create notification
    const notification = {
      user_id: user.id,
      tenant_id: tenant_id,
      type,
      message: title,
      read: false,
      severity,
      context: {
        ...context,
        full_message: message,
        created_by: 'user'
      },
      created_at: new Date().toISOString()
    }

    const { data: createdNotification, error: createError } = await supabase
      .schema('tenants')
      .from('notifications')
      .insert(notification)
      .select()
      .single()

    if (createError) {
      console.error('Error creating notification:', createError)
      return NextResponse.json({ error: 'Failed to create notification' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      notification: createdNotification 
    })

  } catch (error) {
    console.error('Error in notifications POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const notificationId = searchParams.get('id')
    const deleteAll = searchParams.get('all') === 'true'

    if (deleteAll) {
      // Delete all read notifications for the user
      const { error: deleteError } = await supabase
        .schema('tenants')
        .from('notifications')
        .delete()
        .eq('user_id', user.id)
        .eq('read', true)

      if (deleteError) {
        console.error('Error deleting all notifications:', deleteError)
        return NextResponse.json({ error: 'Failed to delete notifications' }, { status: 500 })
      }

      return NextResponse.json({ success: true, message: 'All read notifications deleted' })

    } else if (notificationId) {
      // Delete specific notification
      const { error: deleteError } = await supabase
        .schema('tenants')
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', user.id)

      if (deleteError) {
        console.error('Error deleting notification:', deleteError)
        return NextResponse.json({ error: 'Failed to delete notification' }, { status: 500 })
      }

      return NextResponse.json({ success: true, message: 'Notification deleted' })

    } else {
      return NextResponse.json({ error: 'Notification ID is required' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error in notifications DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

'use client'

import { CopilotKit } from '@copilotkit/react-core'
import { SecurityProvider } from '@/lib/security/context'
import SecurityErrorBoundary from '@/components/security/error-boundary'
import { DeviceTrustBanner } from '@/components/security/device-trust-banner'
import { UserActivityProvider } from '@/components/providers/UserActivityProvider'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <UserActivityProvider>
      <CopilotKit
        runtimeUrl="/api/copilotkit"
        agent="supervisor_agent"
        properties={{
          userRole: 'staff',
          // Deterministic thread ID for tenant isolation
          threadId: crypto.randomUUID() // In production, use org+user ID hash
        }}
      >
        {children}
      </CopilotKit>
    </UserActivityProvider>
  )
}

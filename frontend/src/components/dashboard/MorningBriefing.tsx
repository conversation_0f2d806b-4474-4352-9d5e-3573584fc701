'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Sun,
  Calendar,
  AlertTriangle,
  TrendingUp,
  Clock,
  CheckCircle,
  ArrowRight,
  Coffee,
  Target
} from 'lucide-react'

interface MorningBriefingData {
  greeting: string
  today_summary: {
    critical_deadlines: number
    scheduled_tasks: number
    upcoming_meetings: number
    priority_matters: number
  }
  priority_actions: Array<{
    id: string
    title: string
    description: string
    priority: 'CRITICAL' | 'HIGH' | 'MEDIUM'
    due_time?: string
    estimated_duration?: string
    matter_title?: string
  }>
  insights: Array<{
    id: string
    type: 'deadline_risk' | 'workload_alert' | 'opportunity' | 'recommendation'
    title: string
    description: string
    action_suggested?: string
  }>
  weather_check: {
    message: string
    impact_on_schedule?: string
  }
}

export default function MorningBriefing() {
  const [briefingData, setBriefingData] = useState<MorningBriefingData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dismissed, setDismissed] = useState(false)

  const fetchMorningBriefing = async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if it's morning time (6 AM - 11 AM)
      const now = new Date()
      const hour = now.getHours()
      const isMorningTime = hour >= 6 && hour <= 11

      if (!isMorningTime) {
        setDismissed(true)
        setLoading(false)
        return
      }

      // Check if briefing was already shown today
      const today = now.toDateString()
      const lastShown = localStorage.getItem('morning_briefing_shown')
      if (lastShown === today) {
        setDismissed(true)
        setLoading(false)
        return
      }

      // Fetch morning briefing data
      const response = await fetch('/api/deadline-insights/morning-briefing')
      if (!response.ok) {
        throw new Error('Failed to fetch morning briefing')
      }

      const data = await response.json()
      setBriefingData(data)

      // Mark as shown today
      localStorage.setItem('morning_briefing_shown', today)

    } catch (error) {
      console.error('Error fetching morning briefing:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch morning briefing')
    } finally {
      setLoading(false)
    }
  }

  const handleDismiss = () => {
    setDismissed(true)
    const today = new Date().toDateString()
    localStorage.setItem('morning_briefing_shown', today)
  }

  useEffect(() => {
    fetchMorningBriefing()
  }, [])

  if (loading) {
    return (
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-6 w-48" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="text-center">
                  <Skeleton className="h-8 w-8 mx-auto mb-2" />
                  <Skeleton className="h-4 w-16 mx-auto mb-1" />
                  <Skeleton className="h-3 w-20 mx-auto" />
                </div>
              ))}
            </div>
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || dismissed || !briefingData) {
    return null
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'deadline_risk':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'workload_alert':
        return <Clock className="h-4 w-4 text-orange-500" />
      case 'opportunity':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'recommendation':
        return <Target className="h-4 w-4 text-blue-500" />
      default:
        return <CheckCircle className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <div className="p-2 bg-yellow-100 rounded-full">
              <Sun className="h-5 w-5 text-yellow-600" />
            </div>
            {briefingData.greeting}
          </CardTitle>
          <Button
            onClick={handleDismiss}
            variant="ghost"
            size="sm"
            className="text-blue-600 hover:text-blue-800"
          >
            Dismiss
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Today's Summary */}
        <div>
          <h3 className="flex items-center gap-2 font-semibold text-blue-800 mb-3">
            <Coffee className="h-4 w-4" />
            Today's Overview
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-white rounded-lg shadow-sm">
              <AlertTriangle className="h-6 w-6 mx-auto mb-2 text-red-500" />
              <div className="text-lg font-bold text-red-600">
                {briefingData.today_summary.critical_deadlines}
              </div>
              <div className="text-xs text-gray-600">Critical Deadlines</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg shadow-sm">
              <CheckCircle className="h-6 w-6 mx-auto mb-2 text-blue-500" />
              <div className="text-lg font-bold text-blue-600">
                {briefingData.today_summary.scheduled_tasks}
              </div>
              <div className="text-xs text-gray-600">Scheduled Tasks</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg shadow-sm">
              <Calendar className="h-6 w-6 mx-auto mb-2 text-green-500" />
              <div className="text-lg font-bold text-green-600">
                {briefingData.today_summary.upcoming_meetings}
              </div>
              <div className="text-xs text-gray-600">Meetings</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg shadow-sm">
              <Target className="h-6 w-6 mx-auto mb-2 text-purple-500" />
              <div className="text-lg font-bold text-purple-600">
                {briefingData.today_summary.priority_matters}
              </div>
              <div className="text-xs text-gray-600">Priority Matters</div>
            </div>
          </div>
        </div>

        {/* Priority Actions */}
        {briefingData.priority_actions.length > 0 && (
          <div>
            <h3 className="flex items-center gap-2 font-semibold text-blue-800 mb-3">
              <Target className="h-4 w-4" />
              Priority Actions for Today
            </h3>
            <div className="space-y-3">
              {briefingData.priority_actions.slice(0, 3).map(action => (
                <div key={action.id} className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
                  <div className="flex-shrink-0 mt-1">
                    <Badge className={`text-xs ${getPriorityColor(action.priority)}`}>
                      {action.priority}
                    </Badge>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm text-gray-900">{action.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                      {action.due_time && (
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          Due: {action.due_time}
                        </span>
                      )}
                      {action.estimated_duration && (
                        <span>Est: {action.estimated_duration}</span>
                      )}
                      {action.matter_title && (
                        <span className="font-medium">{action.matter_title}</span>
                      )}
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0 mt-1" />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* AI Insights */}
        {briefingData.insights.length > 0 && (
          <div>
            <h3 className="flex items-center gap-2 font-semibold text-blue-800 mb-3">
              <TrendingUp className="h-4 w-4" />
              AI Insights & Recommendations
            </h3>
            <div className="space-y-3">
              {briefingData.insights.slice(0, 2).map(insight => (
                <Alert key={insight.id} className="bg-white border-blue-200">
                  <div className="flex items-start gap-3">
                    {getInsightIcon(insight.type)}
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{insight.title}</h4>
                      <AlertDescription className="mt-1">
                        {insight.description}
                      </AlertDescription>
                      {insight.action_suggested && (
                        <div className="mt-2 text-sm font-medium text-blue-600">
                          💡 Suggestion: {insight.action_suggested}
                        </div>
                      )}
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          </div>
        )}

        {/* Weather Check */}
        {briefingData.weather_check && (
          <div className="p-3 bg-white rounded-lg shadow-sm border-l-4 border-blue-400">
            <p className="text-sm text-gray-700">{briefingData.weather_check.message}</p>
            {briefingData.weather_check.impact_on_schedule && (
              <p className="text-xs text-gray-500 mt-1">
                📅 {briefingData.weather_check.impact_on_schedule}
              </p>
            )}
          </div>
        )}

        {/* Action Button */}
        <div className="flex justify-center pt-2">
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            <Calendar className="h-4 w-4 mr-2" />
            View Full Schedule
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

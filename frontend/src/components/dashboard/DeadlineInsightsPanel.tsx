'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import {
  AlertTriangle,
  Clock,
  Calendar,
  TrendingUp,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  Info
} from 'lucide-react'
import { useSupabase } from '@/lib/supabase/provider'

interface DeadlineInsight {
  id: string
  title: string
  description: string
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW'
  due_date?: string
  action_required?: string
  matter_id?: string
  matter_title?: string
}

interface DeadlineConflict {
  id: string
  type: string
  description: string
  affected_deadlines: string[]
  severity: 'high' | 'medium' | 'low'
}

interface DeadlineInsightsSummary {
  critical_deadlines: number
  high_risk_deadlines: number
  conflicts_detected: number
  recommendations: number
  last_updated: string
}

interface DeadlineInsightsData {
  summary: DeadlineInsightsSummary
  critical_deadlines: DeadlineInsight[]
  conflicts: DeadlineConflict[]
  loading: boolean
  error: string | null
}

export default function DeadlineInsightsPanel() {
  const { supabase } = useSupabase()
  const [data, setData] = useState<DeadlineInsightsData>({
    summary: {
      critical_deadlines: 0,
      high_risk_deadlines: 0,
      conflicts_detected: 0,
      recommendations: 0,
      last_updated: new Date().toISOString()
    },
    critical_deadlines: [],
    conflicts: [],
    loading: true,
    error: null
  })
  const [refreshing, setRefreshing] = useState(false)

  const fetchDeadlineInsights = async () => {
    try {
      setData(prev => ({ ...prev, loading: true, error: null }))

      // Fetch summary data
      const summaryResponse = await fetch('/api/deadline-insights/summary')
      if (!summaryResponse.ok) {
        throw new Error('Failed to fetch deadline insights summary')
      }
      const summary = await summaryResponse.json()

      // Fetch critical deadlines
      const criticalResponse = await fetch('/api/deadline-insights/critical?limit=5')
      if (!criticalResponse.ok) {
        throw new Error('Failed to fetch critical deadlines')
      }
      const criticalData = await criticalResponse.json()

      // Fetch conflicts
      const conflictsResponse = await fetch('/api/deadline-insights/conflicts')
      if (!conflictsResponse.ok) {
        throw new Error('Failed to fetch deadline conflicts')
      }
      const conflictsData = await conflictsResponse.json()

      setData({
        summary,
        critical_deadlines: criticalData.critical_deadlines || [],
        conflicts: conflictsData.conflicts || [],
        loading: false,
        error: null
      })

    } catch (error) {
      console.error('Error fetching deadline insights:', error)
      setData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch deadline insights'
      }))
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      // Trigger insights generation
      const response = await fetch('/api/deadline-insights/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ force_refresh: true })
      })

      if (response.ok) {
        // Wait a moment then refresh data
        setTimeout(() => {
          fetchDeadlineInsights()
        }, 2000)
      }
    } catch (error) {
      console.error('Error triggering refresh:', error)
    } finally {
      setRefreshing(false)
    }
  }

  useEffect(() => {
    fetchDeadlineInsights()
    
    // Set up periodic refresh every 5 minutes
    const interval = setInterval(fetchDeadlineInsights, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'CRITICAL':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'HIGH':
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      case 'MEDIUM':
        return <Info className="h-4 w-4 text-yellow-500" />
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-green-100 text-green-800 border-green-200'
    }
  }

  const formatRelativeTime = (timestamp: string) => {
    try {
      const now = new Date()
      const time = new Date(timestamp)
      const diffMs = now.getTime() - time.getTime()
      const diffMins = Math.floor(diffMs / 60000)
      
      if (diffMins < 60) return `${diffMins}m ago`
      if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
      return `${Math.floor(diffMins / 1440)}d ago`
    } catch {
      return 'Recently'
    }
  }

  if (data.error) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            <XCircle className="h-5 w-5" />
            Deadline Insights Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{data.error}</AlertDescription>
          </Alert>
          <Button 
            onClick={fetchDeadlineInsights} 
            variant="outline" 
            size="sm" 
            className="mt-3"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6" data-testid="deadline-insights-panel">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Deadlines</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            {data.loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold text-red-600">
                  {data.summary.critical_deadlines}
                </div>
                <p className="text-xs text-muted-foreground">
                  Require immediate attention
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Risk</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            {data.loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold text-orange-600">
                  {data.summary.high_risk_deadlines}
                </div>
                <p className="text-xs text-muted-foreground">
                  Need attention soon
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conflicts</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            {data.loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold text-red-600">
                  {data.summary.conflicts_detected}
                </div>
                <p className="text-xs text-muted-foreground">
                  Scheduling conflicts detected
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recommendations</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            {data.loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold text-blue-600">
                  {data.summary.recommendations}
                </div>
                <p className="text-xs text-muted-foreground">
                  AI-generated suggestions
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Critical Deadlines List */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Critical Deadlines
          </CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Updated {formatRelativeTime(data.summary.last_updated)}
            </span>
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {data.loading ? (
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="flex items-center space-x-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1 space-y-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              ))}
            </div>
          ) : data.critical_deadlines.length > 0 ? (
            <div className="space-y-4">
              {data.critical_deadlines.map(deadline => (
                <div key={deadline.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                  {getPriorityIcon(deadline.priority)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm">{deadline.title}</h4>
                      <Badge className={`text-xs ${getPriorityColor(deadline.priority)}`}>
                        {deadline.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {deadline.description}
                    </p>
                    {deadline.due_date && (
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        Due: {new Date(deadline.due_date).toLocaleDateString()}
                      </div>
                    )}
                    {deadline.action_required && (
                      <div className="mt-2 text-xs font-medium text-blue-600">
                        Action: {deadline.action_required}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
              <p>No critical deadlines at this time</p>
              <p className="text-sm">All deadlines are on track</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Conflicts Section */}
      {data.conflicts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-500" />
              Deadline Conflicts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.conflicts.map(conflict => (
                <Alert key={conflict.id} variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>{conflict.type}:</strong> {conflict.description}
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

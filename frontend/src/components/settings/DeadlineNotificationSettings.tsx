'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import {
  Bell,
  Mail,
  MessageSquare,
  Smartphone,
  Clock,
  AlertTriangle,
  CheckCircle,
  Settings,
  TestTube
} from 'lucide-react'
import { useNotifications } from '@/hooks/useNotifications'

export default function DeadlineNotificationSettings() {
  const { preferences, updatePreferences, sendTestNotification } = useNotifications()
  const [localPreferences, setLocalPreferences] = useState(preferences)
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Update local preferences when global preferences change
  useEffect(() => {
    if (preferences) {
      setLocalPreferences(preferences)
      setHasChanges(false)
    }
  }, [preferences])

  // Track changes
  useEffect(() => {
    if (preferences && localPreferences) {
      setHasChanges(JSON.stringify(preferences) !== JSON.stringify(localPreferences))
    }
  }, [preferences, localPreferences])

  const handleSave = async () => {
    if (!localPreferences) return

    setSaving(true)
    try {
      const success = await updatePreferences(localPreferences)
      if (success) {
        toast.success('Notification preferences saved successfully')
        setHasChanges(false)
      } else {
        toast.error('Failed to save notification preferences')
      }
    } catch (error) {
      toast.error('An error occurred while saving preferences')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    if (preferences) {
      setLocalPreferences(preferences)
      setHasChanges(false)
    }
  }

  const handleTestNotification = async () => {
    try {
      // Get tenant ID from user context or current tenant
      const tenantId = 'current-tenant' // This would come from context
      const success = await sendTestNotification(tenantId)
      if (success) {
        toast.success('Test notification sent! Check your notifications.')
      } else {
        toast.error('Failed to send test notification')
      }
    } catch (error) {
      toast.error('An error occurred while sending test notification')
    }
  }

  const updateLocalPreference = (path: string, value: any) => {
    if (!localPreferences) return

    setLocalPreferences(prev => {
      if (!prev) return prev
      
      const keys = path.split('.')
      const newPrefs = { ...prev }
      let current: any = newPrefs
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] }
        current = current[keys[i]]
      }
      
      current[keys[keys.length - 1]] = value
      return newPrefs
    })
  }

  if (!localPreferences) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading notification preferences...</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Deadline Notifications</h2>
          <p className="text-muted-foreground">
            Configure how and when you receive deadline alerts and insights
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleTestNotification}
            className="flex items-center gap-2"
          >
            <TestTube className="h-4 w-4" />
            Test Notification
          </Button>
          {hasChanges && (
            <Badge variant="secondary">Unsaved changes</Badge>
          )}
        </div>
      </div>

      {/* Alert Types */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Alert Types
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="deadline-alerts">Deadline Alerts</Label>
              <p className="text-sm text-muted-foreground">
                Get notified about upcoming and critical deadlines
              </p>
            </div>
            <Switch
              id="deadline-alerts"
              checked={localPreferences.deadline_alerts}
              onCheckedChange={(checked) => updateLocalPreference('deadline_alerts', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="conflict-alerts">Conflict Alerts</Label>
              <p className="text-sm text-muted-foreground">
                Get notified when deadline conflicts are detected
              </p>
            </div>
            <Switch
              id="conflict-alerts"
              checked={localPreferences.conflict_alerts}
              onCheckedChange={(checked) => updateLocalPreference('conflict_alerts', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="risk-alerts">Risk Alerts</Label>
              <p className="text-sm text-muted-foreground">
                Get notified about high-risk deadlines and potential issues
              </p>
            </div>
            <Switch
              id="risk-alerts"
              checked={localPreferences.risk_alerts}
              onCheckedChange={(checked) => updateLocalPreference('risk_alerts', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="return-alerts">Return Alerts</Label>
              <p className="text-sm text-muted-foreground">
                Get personalized insights when you return after being away
              </p>
            </div>
            <Switch
              id="return-alerts"
              checked={localPreferences.return_alerts}
              onCheckedChange={(checked) => updateLocalPreference('return_alerts', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="morning-briefing">Morning Briefing</Label>
              <p className="text-sm text-muted-foreground">
                Get a daily summary of deadlines and priorities
              </p>
            </div>
            <Switch
              id="morning-briefing"
              checked={localPreferences.morning_briefing}
              onCheckedChange={(checked) => updateLocalPreference('morning_briefing', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Channels */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Channels
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <div>
                <Label htmlFor="in-app">In-App Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Show notifications within the application
                </p>
              </div>
            </div>
            <Switch
              id="in-app"
              checked={localPreferences.channels.in_app}
              onCheckedChange={(checked) => updateLocalPreference('channels.in_app', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              <div>
                <Label htmlFor="email">Email Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Send notifications to your email address
                </p>
              </div>
            </div>
            <Switch
              id="email"
              checked={localPreferences.channels.email}
              onCheckedChange={(checked) => updateLocalPreference('channels.email', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <div>
                <Label htmlFor="sms">SMS Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Send critical alerts via SMS (charges may apply)
                </p>
              </div>
            </div>
            <Switch
              id="sms"
              checked={localPreferences.channels.sms}
              onCheckedChange={(checked) => updateLocalPreference('channels.sms', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              <div>
                <Label htmlFor="push">Push Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Send push notifications to your devices
                </p>
              </div>
            </div>
            <Switch
              id="push"
              checked={localPreferences.channels.push}
              onCheckedChange={(checked) => updateLocalPreference('channels.push', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Priority Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Priority Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground mb-4">
            Choose which priority levels trigger notifications
          </p>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <Label htmlFor="critical">Critical</Label>
              </div>
              <Switch
                id="critical"
                checked={localPreferences.priorities.critical}
                onCheckedChange={(checked) => updateLocalPreference('priorities.critical', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <Label htmlFor="high">High</Label>
              </div>
              <Switch
                id="high"
                checked={localPreferences.priorities.high}
                onCheckedChange={(checked) => updateLocalPreference('priorities.high', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <Label htmlFor="medium">Medium</Label>
              </div>
              <Switch
                id="medium"
                checked={localPreferences.priorities.medium}
                onCheckedChange={(checked) => updateLocalPreference('priorities.medium', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <Label htmlFor="low">Low</Label>
              </div>
              <Switch
                id="low"
                checked={localPreferences.priorities.low}
                onCheckedChange={(checked) => updateLocalPreference('priorities.low', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timing Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Timing Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="frequency">Notification Frequency</Label>
            <Select
              value={localPreferences.frequency}
              onValueChange={(value) => updateLocalPreference('frequency', value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="immediate">Immediate</SelectItem>
                <SelectItem value="hourly">Hourly Digest</SelectItem>
                <SelectItem value="daily">Daily Digest</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div>
            <div className="flex items-center justify-between mb-4">
              <div>
                <Label htmlFor="quiet-hours">Quiet Hours</Label>
                <p className="text-sm text-muted-foreground">
                  Disable notifications during specified hours
                </p>
              </div>
              <Switch
                id="quiet-hours"
                checked={localPreferences.quiet_hours.enabled}
                onCheckedChange={(checked) => updateLocalPreference('quiet_hours.enabled', checked)}
              />
            </div>

            {localPreferences.quiet_hours.enabled && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="quiet-start">Start Time</Label>
                  <Input
                    id="quiet-start"
                    type="time"
                    value={localPreferences.quiet_hours.start}
                    onChange={(e) => updateLocalPreference('quiet_hours.start', e.target.value)}
                    className="mt-2"
                  />
                </div>
                <div>
                  <Label htmlFor="quiet-end">End Time</Label>
                  <Input
                    id="quiet-end"
                    type="time"
                    value={localPreferences.quiet_hours.end}
                    onChange={(e) => updateLocalPreference('quiet_hours.end', e.target.value)}
                    className="mt-2"
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Save Actions */}
      <div className="flex items-center justify-between">
        <Alert className="flex-1 mr-4">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Changes are saved automatically when you modify settings.
            {hasChanges && ' You have unsaved changes.'}
          </AlertDescription>
        </Alert>

        <div className="flex items-center gap-2">
          {hasChanges && (
            <Button variant="outline" onClick={handleReset}>
              Reset
            </Button>
          )}
          <Button 
            onClick={handleSave} 
            disabled={!hasChanges || saving}
            className="min-w-[100px]"
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  )
}

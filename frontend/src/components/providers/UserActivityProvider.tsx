'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useUserActivity } from '@/hooks/useUserActivity'
import { useNotifications } from '@/hooks/useNotifications'
import { toast } from 'sonner'

interface UserActivityContextType {
  lastActivity: Date | null
  timeAway: number
  shouldTriggerInsights: boolean
  isActive: boolean
  unreadNotifications: number
  triggerUserReturnInsights: () => Promise<any>
  sendHeartbeat: () => Promise<void>
}

const UserActivityContext = createContext<UserActivityContextType | null>(null)

export function useUserActivityContext() {
  const context = useContext(UserActivityContext)
  if (!context) {
    throw new Error('useUserActivityContext must be used within a UserActivityProvider')
  }
  return context
}

interface UserActivityProviderProps {
  children: React.ReactNode
}

export function UserActivityProvider({ children }: UserActivityProviderProps) {
  const [hasShownReturnMessage, setHasShownReturnMessage] = useState(false)
  
  // Initialize user activity tracking
  const userActivity = useUserActivity({
    heartbeatInterval: 5 * 60 * 1000, // 5 minutes
    insightsThreshold: 1, // 1 hour
    enableHeartbeat: true,
    enableReturnDetection: true
  })

  // Initialize notifications
  const { unreadCount, fetchNotifications } = useNotifications()

  // Show welcome back message when user returns after being away
  useEffect(() => {
    if (userActivity.timeAway >= 1 && userActivity.isActive && !hasShownReturnMessage) {
      const hours = Math.floor(userActivity.timeAway)
      const minutes = Math.floor((userActivity.timeAway - hours) * 60)
      
      let timeAwayText = ''
      if (hours > 0) {
        timeAwayText = hours === 1 ? '1 hour' : `${hours} hours`
        if (minutes > 0) {
          timeAwayText += ` and ${minutes} minute${minutes === 1 ? '' : 's'}`
        }
      } else {
        timeAwayText = `${minutes} minute${minutes === 1 ? '' : 's'}`
      }

      // Show welcome back toast
      toast.info(`Welcome back! You've been away for ${timeAwayText}`, {
        description: userActivity.shouldTriggerInsights 
          ? 'We\'re generating fresh insights for you...' 
          : 'Your deadlines are up to date.',
        duration: 5000,
        action: userActivity.shouldTriggerInsights ? {
          label: 'View Insights',
          onClick: () => {
            // Scroll to deadline insights panel
            const insightsPanel = document.querySelector('[data-testid="deadline-insights-panel"]')
            if (insightsPanel) {
              insightsPanel.scrollIntoView({ behavior: 'smooth' })
            }
          }
        } : undefined
      })

      setHasShownReturnMessage(true)
      
      // Reset the flag after some time
      setTimeout(() => {
        setHasShownReturnMessage(false)
      }, 30 * 60 * 1000) // 30 minutes
    }
  }, [userActivity.timeAway, userActivity.isActive, userActivity.shouldTriggerInsights, hasShownReturnMessage])

  // Show notifications for critical deadlines
  useEffect(() => {
    if (unreadCount > 0) {
      // Fetch latest notifications to check for critical ones
      fetchNotifications({ includeRead: false, severity: 'critical' })
        .then(() => {
          // This would be handled by the notifications hook's real-time updates
        })
        .catch(console.error)
    }
  }, [unreadCount, fetchNotifications])

  // Context value
  const contextValue: UserActivityContextType = {
    lastActivity: userActivity.lastActivity,
    timeAway: userActivity.timeAway,
    shouldTriggerInsights: userActivity.shouldTriggerInsights,
    isActive: userActivity.isActive,
    unreadNotifications: unreadCount,
    triggerUserReturnInsights: userActivity.triggerUserReturnInsights || (() => Promise.resolve()),
    sendHeartbeat: userActivity.sendHeartbeat
  }

  return (
    <UserActivityContext.Provider value={contextValue}>
      {children}
    </UserActivityContext.Provider>
  )
}

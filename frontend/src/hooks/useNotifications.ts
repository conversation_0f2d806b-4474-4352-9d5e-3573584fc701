'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSupabase } from '@/lib/supabase/provider'

interface Notification {
  id: string
  user_id: string
  tenant_id: string
  type: string
  message: string
  read: boolean
  severity: 'low' | 'medium' | 'high' | 'critical'
  context: any
  sender_id?: string
  created_at: string
  updated_at?: string
}

interface NotificationPreferences {
  deadline_alerts: boolean
  email_alerts: boolean
  sms_alerts: boolean
  return_alerts: boolean
  morning_briefing: boolean
  conflict_alerts: boolean
  risk_alerts: boolean
  frequency: 'immediate' | 'hourly' | 'daily'
  quiet_hours: {
    enabled: boolean
    start: string
    end: string
  }
  channels: {
    in_app: boolean
    email: boolean
    sms: boolean
    push: boolean
  }
  priorities: {
    critical: boolean
    high: boolean
    medium: boolean
    low: boolean
  }
}

interface UseNotificationsState {
  notifications: Notification[]
  unreadCount: number
  loading: boolean
  error: string | null
  preferences: NotificationPreferences | null
}

export function useNotifications() {
  const { supabase } = useSupabase()
  const [state, setState] = useState<UseNotificationsState>({
    notifications: [],
    unreadCount: 0,
    loading: true,
    error: null,
    preferences: null
  })

  // Fetch notifications
  const fetchNotifications = useCallback(async (options: {
    limit?: number
    includeRead?: boolean
    type?: string
    severity?: string
  } = {}) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))

      const params = new URLSearchParams()
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.includeRead) params.append('include_read', 'true')
      if (options.type) params.append('type', options.type)
      if (options.severity) params.append('severity', options.severity)

      const response = await fetch(`/api/notifications?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch notifications')
      }

      const data = await response.json()
      setState(prev => ({
        ...prev,
        notifications: data.notifications || [],
        unreadCount: data.unread_count || 0,
        loading: false
      }))

    } catch (error) {
      console.error('Error fetching notifications:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch notifications',
        loading: false
      }))
    }
  }, [])

  // Fetch notification preferences
  const fetchPreferences = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/preferences')
      if (!response.ok) {
        throw new Error('Failed to fetch preferences')
      }

      const data = await response.json()
      setState(prev => ({
        ...prev,
        preferences: data.preferences
      }))

    } catch (error) {
      console.error('Error fetching notification preferences:', error)
    }
  }, [])

  // Update notification preferences
  const updatePreferences = useCallback(async (preferences: Partial<NotificationPreferences>) => {
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ preferences })
      })

      if (!response.ok) {
        throw new Error('Failed to update preferences')
      }

      setState(prev => ({
        ...prev,
        preferences: prev.preferences ? { ...prev.preferences, ...preferences } : null
      }))

      return true
    } catch (error) {
      console.error('Error updating notification preferences:', error)
      return false
    }
  }, [])

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'mark_read', notification_id: notificationId })
      })

      if (!response.ok) {
        throw new Error('Failed to mark notification as read')
      }

      // Update local state
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        ),
        unreadCount: Math.max(0, prev.unreadCount - 1)
      }))

      return true
    } catch (error) {
      console.error('Error marking notification as read:', error)
      return false
    }
  }, [])

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'mark_all_read' })
      })

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read')
      }

      // Update local state
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => ({ ...n, read: true })),
        unreadCount: 0
      }))

      return true
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      return false
    }
  }, [])

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications?id=${notificationId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete notification')
      }

      // Update local state
      setState(prev => {
        const notification = prev.notifications.find(n => n.id === notificationId)
        const wasUnread = notification && !notification.read
        
        return {
          ...prev,
          notifications: prev.notifications.filter(n => n.id !== notificationId),
          unreadCount: wasUnread ? Math.max(0, prev.unreadCount - 1) : prev.unreadCount
        }
      })

      return true
    } catch (error) {
      console.error('Error deleting notification:', error)
      return false
    }
  }, [])

  // Send test notification
  const sendTestNotification = useCallback(async (tenantId: string) => {
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'test_notification', tenant_id: tenantId })
      })

      if (!response.ok) {
        throw new Error('Failed to send test notification')
      }

      // Refresh notifications to show the test notification
      setTimeout(() => fetchNotifications(), 1000)

      return true
    } catch (error) {
      console.error('Error sending test notification:', error)
      return false
    }
  }, [fetchNotifications])

  // Set up real-time subscription for new notifications
  useEffect(() => {
    if (!supabase) return

    const channel = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'tenants',
          table: 'notifications'
        },
        (payload) => {
          const newNotification = payload.new as Notification
          setState(prev => ({
            ...prev,
            notifications: [newNotification, ...prev.notifications],
            unreadCount: prev.unreadCount + 1
          }))
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'tenants',
          table: 'notifications'
        },
        (payload) => {
          const updatedNotification = payload.new as Notification
          setState(prev => ({
            ...prev,
            notifications: prev.notifications.map(n => 
              n.id === updatedNotification.id ? updatedNotification : n
            )
          }))
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [supabase])

  // Initial data fetch
  useEffect(() => {
    fetchNotifications()
    fetchPreferences()
  }, [fetchNotifications, fetchPreferences])

  return {
    ...state,
    fetchNotifications,
    fetchPreferences,
    updatePreferences,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    sendTestNotification,
    refresh: () => {
      fetchNotifications()
      fetchPreferences()
    }
  }
}

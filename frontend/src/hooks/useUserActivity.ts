'use client'

import { useEffect, useRef, useState } from 'react'
import { useSupabase } from '@/lib/supabase/provider'

interface UserActivityState {
  lastActivity: Date | null
  timeAway: number // in hours
  shouldTriggerInsights: boolean
  isActive: boolean
}

interface UseUserActivityOptions {
  heartbeatInterval?: number // in milliseconds, default 5 minutes
  insightsThreshold?: number // in hours, default 1 hour
  enableHeartbeat?: boolean // default true
  enableReturnDetection?: boolean // default true
}

export function useUserActivity(options: UseUserActivityOptions = {}) {
  const {
    heartbeatInterval = 5 * 60 * 1000, // 5 minutes
    insightsThreshold = 1, // 1 hour
    enableHeartbeat = true,
    enableReturnDetection = true
  } = options

  const { supabase } = useSupabase()
  const [state, setState] = useState<UserActivityState>({
    lastActivity: null,
    timeAway: 0,
    shouldTriggerInsights: false,
    isActive: true
  })

  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastActivityRef = useRef<Date | null>(null)
  const hasTriggeredInsightsRef = useRef(false)

  // Update activity timestamp
  const updateActivity = () => {
    const now = new Date()
    lastActivityRef.current = now
    setState(prev => ({
      ...prev,
      lastActivity: now,
      timeAway: 0,
      isActive: true
    }))
  }

  // Send heartbeat to server
  const sendHeartbeat = async () => {
    try {
      await fetch('/api/user/activity', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'heartbeat' })
      })
    } catch (error) {
      console.error('Error sending heartbeat:', error)
    }
  }

  // Check for user return and trigger insights if needed
  const checkUserReturn = async () => {
    try {
      const response = await fetch('/api/user/activity')
      if (response.ok) {
        const data = await response.json()
        const timeAwayHours = data.time_away_hours || 0
        
        setState(prev => ({
          ...prev,
          timeAway: timeAwayHours,
          shouldTriggerInsights: timeAwayHours >= insightsThreshold
        }))

        // Trigger insights if user was away long enough and hasn't been triggered yet
        if (timeAwayHours >= insightsThreshold && 
            !hasTriggeredInsightsRef.current && 
            enableReturnDetection) {
          
          await triggerUserReturnInsights(data.last_activity_at)
          hasTriggeredInsightsRef.current = true
          
          // Reset the flag after some time
          setTimeout(() => {
            hasTriggeredInsightsRef.current = false
          }, 4 * 60 * 60 * 1000) // 4 hours
        }
      }
    } catch (error) {
      console.error('Error checking user return:', error)
    }
  }

  // Trigger user return insights
  const triggerUserReturnInsights = async (lastActivityTime: string) => {
    try {
      const response = await fetch('/api/user/activity', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'user_return',
          last_activity_time: lastActivityTime
        })
      })

      if (response.ok) {
        const data = await response.json()
        console.log('User return insights triggered:', data)
        return data
      }
    } catch (error) {
      console.error('Error triggering user return insights:', error)
    }
  }

  // Handle visibility change (tab focus/blur)
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // User switched away from tab
      setState(prev => ({ ...prev, isActive: false }))
    } else {
      // User returned to tab
      setState(prev => ({ ...prev, isActive: true }))
      updateActivity()
      
      // Check if we should trigger return insights
      if (enableReturnDetection) {
        setTimeout(checkUserReturn, 1000) // Small delay to ensure state is updated
      }
    }
  }

  // Handle user interaction events
  const handleUserInteraction = () => {
    updateActivity()
  }

  // Set up event listeners and intervals
  useEffect(() => {
    // Initial activity update
    updateActivity()

    // Check for user return on mount
    if (enableReturnDetection) {
      checkUserReturn()
    }

    // Set up heartbeat interval
    if (enableHeartbeat) {
      heartbeatIntervalRef.current = setInterval(() => {
        if (!document.hidden) {
          sendHeartbeat()
        }
      }, heartbeatInterval)
    }

    // Set up event listeners
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { passive: true })
    })

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Cleanup
    return () => {
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
      }

      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction)
      })

      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [heartbeatInterval, enableHeartbeat, enableReturnDetection, insightsThreshold])

  // Calculate time away periodically
  useEffect(() => {
    const calculateTimeAway = () => {
      if (lastActivityRef.current) {
        const now = new Date()
        const timeAwayMs = now.getTime() - lastActivityRef.current.getTime()
        const timeAwayHours = timeAwayMs / (1000 * 60 * 60)
        
        setState(prev => ({
          ...prev,
          timeAway: timeAwayHours,
          shouldTriggerInsights: timeAwayHours >= insightsThreshold
        }))
      }
    }

    const interval = setInterval(calculateTimeAway, 60000) // Update every minute
    return () => clearInterval(interval)
  }, [insightsThreshold])

  return {
    ...state,
    updateActivity,
    triggerUserReturnInsights: () => {
      if (lastActivityRef.current) {
        return triggerUserReturnInsights(lastActivityRef.current.toISOString())
      }
    },
    sendHeartbeat
  }
}

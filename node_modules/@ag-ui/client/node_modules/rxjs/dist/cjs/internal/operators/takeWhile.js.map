{"version": 3, "file": "takeWhile.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/takeWhile.ts"], "names": [], "mappings": ";;;AACA,qCAAuC;AACvC,2DAAgE;AAoDhE,SAAgB,SAAS,CAAI,SAA+C,EAAE,SAAiB;IAAjB,0BAAA,EAAA,iBAAiB;IAC7F,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,CAAC,SAAS,CACd,6CAAwB,CAAC,UAAU,EAAE,UAAC,KAAK;YACzC,IAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;YACzC,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,CAAC,MAAM,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAXD,8BAWC"}
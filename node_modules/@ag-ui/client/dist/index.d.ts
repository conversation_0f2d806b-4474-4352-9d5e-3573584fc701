import { ApplyEvents, BaseEvent, Message, State, RunAgentInput, RunAgent } from '@ag-ui/core';
export * from '@ag-ui/core';
import { Observable } from 'rxjs';
import { z } from 'zod';

declare const defaultApplyEvents: (...args: Parameters<ApplyEvents>) => ReturnType<ApplyEvents>;

declare const verifyEvents: (debug: boolean) => (source$: Observable<BaseEvent>) => Observable<BaseEvent>;

declare enum HttpEventType {
    HEADERS = "headers",
    DATA = "data"
}
interface HttpDataEvent {
    type: HttpEventType.DATA;
    data?: Uint8Array;
}
interface HttpHeadersEvent {
    type: HttpEventType.HEADERS;
    status: number;
    headers: Headers;
}
type HttpEvent = HttpDataEvent | HttpHeadersEvent;
declare const runHttpRequest: (url: string, requestInit: RequestInit) => Observable<HttpEvent>;

/**
 * Transforms HTTP events into BaseEvents using the appropriate format parser based on content type.
 */
declare const transformHttpEventStream: (source$: Observable<HttpEvent>) => Observable<BaseEvent>;

/**
 * Parses a stream of HTTP events into a stream of JSON objects using Server-Sent Events (SSE) format.
 * Strictly follows the SSE standard where:
 * - Events are separated by double newlines ('\n\n')
 * - Only 'data:' prefixed lines are processed
 * - Multi-line data events are supported and joined
 * - Non-data fields (event, id, retry) are ignored
 */
declare const parseSSEStream: (source$: Observable<HttpEvent>) => Observable<any>;

/**
 * Parses a stream of HTTP events into a stream of BaseEvent objects using Protocol Buffer format.
 * Each message is prefixed with a 4-byte length header (uint32 in big-endian format)
 * followed by the protocol buffer encoded message.
 */
declare const parseProtoStream: (source$: Observable<HttpEvent>) => Observable<BaseEvent>;

declare const LegacyRuntimeProtocolEvent: z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
    type: z.ZodLiteral<"TextMessageStart">;
    messageId: z.ZodString;
    parentMessageId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    messageId: string;
    type: "TextMessageStart";
    parentMessageId?: string | undefined;
}, {
    messageId: string;
    type: "TextMessageStart";
    parentMessageId?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"TextMessageContent">;
    messageId: z.ZodString;
    content: z.ZodString;
}, "strip", z.ZodTypeAny, {
    messageId: string;
    type: "TextMessageContent";
    content: string;
}, {
    messageId: string;
    type: "TextMessageContent";
    content: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"TextMessageEnd">;
    messageId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    messageId: string;
    type: "TextMessageEnd";
}, {
    messageId: string;
    type: "TextMessageEnd";
}>, z.ZodObject<{
    type: z.ZodLiteral<"ActionExecutionStart">;
    actionExecutionId: z.ZodString;
    actionName: z.ZodString;
    parentMessageId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "ActionExecutionStart";
    actionExecutionId: string;
    actionName: string;
    parentMessageId?: string | undefined;
}, {
    type: "ActionExecutionStart";
    actionExecutionId: string;
    actionName: string;
    parentMessageId?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"ActionExecutionArgs">;
    actionExecutionId: z.ZodString;
    args: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "ActionExecutionArgs";
    actionExecutionId: string;
    args: string;
}, {
    type: "ActionExecutionArgs";
    actionExecutionId: string;
    args: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"ActionExecutionEnd">;
    actionExecutionId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "ActionExecutionEnd";
    actionExecutionId: string;
}, {
    type: "ActionExecutionEnd";
    actionExecutionId: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"ActionExecutionResult">;
    actionName: z.ZodString;
    actionExecutionId: z.ZodString;
    result: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "ActionExecutionResult";
    actionExecutionId: string;
    actionName: string;
    result: string;
}, {
    type: "ActionExecutionResult";
    actionExecutionId: string;
    actionName: string;
    result: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"AgentStateMessage">;
    threadId: z.ZodString;
    agentName: z.ZodString;
    nodeName: z.ZodString;
    runId: z.ZodString;
    active: z.ZodBoolean;
    role: z.ZodString;
    state: z.ZodString;
    running: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    role: string;
    type: "AgentStateMessage";
    threadId: string;
    agentName: string;
    nodeName: string;
    runId: string;
    active: boolean;
    state: string;
    running: boolean;
}, {
    role: string;
    type: "AgentStateMessage";
    threadId: string;
    agentName: string;
    nodeName: string;
    runId: string;
    active: boolean;
    state: string;
    running: boolean;
}>, z.ZodObject<{
    type: z.ZodLiteral<"MetaEvent">;
    name: z.ZodEnum<["LangGraphInterruptEvent", "PredictState", "Exit"]>;
    value: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    name: "PredictState" | "LangGraphInterruptEvent" | "Exit";
    type: "MetaEvent";
    value?: any;
}, {
    name: "PredictState" | "LangGraphInterruptEvent" | "Exit";
    type: "MetaEvent";
    value?: any;
}>]>;
type LegacyRuntimeProtocolEvent = z.infer<typeof LegacyRuntimeProtocolEvent>;

declare const convertToLegacyEvents: (threadId: string, runId: string, agentName: string) => (events$: Observable<BaseEvent>) => Observable<LegacyRuntimeProtocolEvent>;

interface AgentConfig {
    agentId?: string;
    description?: string;
    threadId?: string;
    initialMessages?: Message[];
    initialState?: State;
    debug?: boolean;
}
interface HttpAgentConfig extends AgentConfig {
    url: string;
    headers?: Record<string, string>;
}
type RunAgentParameters = Partial<Pick<RunAgentInput, "runId" | "tools" | "context" | "forwardedProps">>;

declare abstract class AbstractAgent {
    agentId?: string;
    description: string;
    threadId: string;
    messages: Message[];
    state: State;
    constructor({ agentId, description, threadId, initialMessages, initialState, debug, }?: AgentConfig);
    protected abstract run(...args: Parameters<RunAgent>): ReturnType<RunAgent>;
    runAgent(parameters?: RunAgentParameters): Promise<void>;
    abortRun(): void;
    protected apply(...args: Parameters<ApplyEvents>): ReturnType<ApplyEvents>;
    protected processApplyEvents(input: RunAgentInput, events$: ReturnType<ApplyEvents>): ReturnType<ApplyEvents>;
    protected prepareRunAgentInput(parameters?: RunAgentParameters): RunAgentInput;
    protected onError(error: Error): void;
    protected onFinalize(): void;
    clone(): any;
    legacy_to_be_removed_runAgentBridged(config?: RunAgentParameters): Observable<LegacyRuntimeProtocolEvent>;
}

interface RunHttpAgentConfig extends RunAgentParameters {
    abortController?: AbortController;
}
declare class HttpAgent extends AbstractAgent {
    url: string;
    headers: Record<string, string>;
    abortController: AbortController;
    /**
     * Returns the fetch config for the http request.
     * Override this to customize the request.
     *
     * @returns The fetch config for the http request.
     */
    protected requestInit(input: RunAgentInput): RequestInit;
    runAgent(parameters?: RunHttpAgentConfig): Promise<void>;
    abortRun(): void;
    constructor(config: HttpAgentConfig);
    run(input: RunAgentInput): Observable<BaseEvent>;
}

export { AbstractAgent, type AgentConfig, HttpAgent, convertToLegacyEvents, defaultApplyEvents, parseProtoStream, parseSSEStream, runHttpRequest, transformHttpEventStream, verifyEvents };

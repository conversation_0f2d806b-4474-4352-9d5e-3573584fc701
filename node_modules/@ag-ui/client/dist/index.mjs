var B=Object.defineProperty,$=Object.defineProperties;var K=Object.getOwnPropertyDescriptors;var j=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,q=Object.prototype.propertyIsEnumerable;var k=(u,n,t)=>n in u?B(u,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[n]=t,L=(u,n)=>{for(var t in n||(n={}))V.call(n,t)&&k(u,t,n[t]);if(j)for(var t of j(n))q.call(n,t)&&k(u,t,n[t]);return u},M=(u,n)=>$(u,K(n));import{EventType as v}from"@ag-ui/core";import{mergeMap as W}from"rxjs/operators";var _=u=>{if(typeof structuredClone=="function")return structuredClone(u);try{return JSON.parse(JSON.stringify(u))}catch(n){return L({},u)}};import{applyPatch as Y}from"fast-json-patch";import Q from"untruncate-json";var w=(...u)=>{let[n,t]=u,e=_(n.messages),s=_(n.state),i,l=r=>[_(r)],o=()=>[];return t.pipe(W(r=>{var p;switch(r.type){case v.TEXT_MESSAGE_START:{let{messageId:d,role:c}=r,g={id:d,role:c,content:""};return e.push(g),l({messages:e})}case v.TEXT_MESSAGE_CONTENT:{let{delta:d}=r,c=e[e.length-1];return c.content=c.content+d,l({messages:e})}case v.TEXT_MESSAGE_END:return o();case v.TOOL_CALL_START:{let{toolCallId:d,toolCallName:c,parentMessageId:g}=r,E;return g&&e.length>0&&e[e.length-1].id===g?E=e[e.length-1]:(E={id:g||d,role:"assistant",toolCalls:[]},e.push(E)),(p=E.toolCalls)!=null||(E.toolCalls=[]),E.toolCalls.push({id:d,type:"function",function:{name:c,arguments:""}}),l({messages:e})}case v.TOOL_CALL_ARGS:{let{delta:d}=r,c=e[e.length-1],g=c.toolCalls[c.toolCalls.length-1];if(g.function.arguments+=d,i){let E=i.find(h=>h.tool===g.function.name);if(E)try{let h=JSON.parse(Q(g.function.arguments));return E.tool_argument&&E.tool_argument in h?(s=M(L({},s),{[E.state_key]:h[E.tool_argument]}),l({messages:e,state:s})):(s=M(L({},s),{[E.state_key]:h}),l({messages:e,state:s}))}catch(h){}}return l({messages:e})}case v.TOOL_CALL_END:return o();case v.STATE_SNAPSHOT:{let{snapshot:d}=r;return s=d,l({state:s})}case v.STATE_DELTA:{let{delta:d}=r;try{return s=Y(s,d,!0,!1).newDocument,l({state:s})}catch(c){let g=c instanceof Error?c.message:String(c);return console.warn(`Failed to apply state patch:
Current state: ${JSON.stringify(s,null,2)}
Patch operations: ${JSON.stringify(d,null,2)}
Error: ${g}`),o()}}case v.MESSAGES_SNAPSHOT:{let{messages:d}=r;return e=d,l({messages:e})}case v.RAW:return o();case v.CUSTOM:{let d=r;return d.name==="PredictState"&&(i=d.value),o()}case v.RUN_STARTED:return o();case v.RUN_FINISHED:return o();case v.RUN_ERROR:return o();case v.STEP_STARTED:return o();case v.STEP_FINISHED:return i=void 0,o();case v.TEXT_MESSAGE_CHUNK:throw new Error("TEXT_MESSAGE_CHUNK must be tranformed before being applied");case v.TOOL_CALL_CHUNK:throw new Error("TOOL_CALL_CHUNK must be tranformed before being applied")}let f=r.type;return o()}))};import{EventType as S,AGUIError as A}from"@ag-ui/core";import{throwError as y,of as x}from"rxjs";import{mergeMap as Z}from"rxjs/operators";var N=u=>n=>{let t,e,s=!1,i=!1,l=!1,o=new Map;return n.pipe(Z(r=>{let f=r.type;if(u&&console.debug("[VERIFY]:",JSON.stringify(r)),i)return y(()=>new A(`Cannot send event type '${f}': The run has already errored with 'RUN_ERROR'. No further events can be sent.`));if(s&&f!==S.RUN_ERROR)return y(()=>new A(`Cannot send event type '${f}': The run has already finished with 'RUN_FINISHED'. Start a new run with 'RUN_STARTED'.`));if(t!==void 0&&![S.TEXT_MESSAGE_CONTENT,S.TEXT_MESSAGE_END,S.RAW].includes(f))return y(()=>new A(`Cannot send event type '${f}' after 'TEXT_MESSAGE_START': Send 'TEXT_MESSAGE_END' first.`));if(e!==void 0&&![S.TOOL_CALL_ARGS,S.TOOL_CALL_END,S.RAW].includes(f))return f===S.TOOL_CALL_START?y(()=>new A("Cannot send 'TOOL_CALL_START' event: A tool call is already in progress. Complete it with 'TOOL_CALL_END' first.")):y(()=>new A(`Cannot send event type '${f}' after 'TOOL_CALL_START': Send 'TOOL_CALL_END' first.`));if(l){if(f===S.RUN_STARTED)return y(()=>new A("Cannot send multiple 'RUN_STARTED' events: A 'RUN_STARTED' event was already sent. Each run must have exactly one 'RUN_STARTED' event at the beginning."))}else if(l=!0,f!==S.RUN_STARTED&&f!==S.RUN_ERROR)return y(()=>new A("First event must be 'RUN_STARTED'"));switch(f){case S.TEXT_MESSAGE_START:return t!==void 0?y(()=>new A("Cannot send 'TEXT_MESSAGE_START' event: A text message is already in progress. Complete it with 'TEXT_MESSAGE_END' first.")):(t=r.messageId,x(r));case S.TEXT_MESSAGE_CONTENT:return t===void 0?y(()=>new A("Cannot send 'TEXT_MESSAGE_CONTENT' event: No active text message found. Start a text message with 'TEXT_MESSAGE_START' first.")):r.messageId!==t?y(()=>new A(`Cannot send 'TEXT_MESSAGE_CONTENT' event: Message ID mismatch. The ID '${r.messageId}' doesn't match the active message ID '${t}'.`)):x(r);case S.TEXT_MESSAGE_END:return t===void 0?y(()=>new A("Cannot send 'TEXT_MESSAGE_END' event: No active text message found. A 'TEXT_MESSAGE_START' event must be sent first.")):r.messageId!==t?y(()=>new A(`Cannot send 'TEXT_MESSAGE_END' event: Message ID mismatch. The ID '${r.messageId}' doesn't match the active message ID '${t}'.`)):(t=void 0,x(r));case S.TOOL_CALL_START:return e!==void 0?y(()=>new A("Cannot send 'TOOL_CALL_START' event: A tool call is already in progress. Complete it with 'TOOL_CALL_END' first.")):(e=r.toolCallId,x(r));case S.TOOL_CALL_ARGS:return e===void 0?y(()=>new A("Cannot send 'TOOL_CALL_ARGS' event: No active tool call found. Start a tool call with 'TOOL_CALL_START' first.")):r.toolCallId!==e?y(()=>new A(`Cannot send 'TOOL_CALL_ARGS' event: Tool call ID mismatch. The ID '${r.toolCallId}' doesn't match the active tool call ID '${e}'.`)):x(r);case S.TOOL_CALL_END:return e===void 0?y(()=>new A("Cannot send 'TOOL_CALL_END' event: No active tool call found. A 'TOOL_CALL_START' event must be sent first.")):r.toolCallId!==e?y(()=>new A(`Cannot send 'TOOL_CALL_END' event: Tool call ID mismatch. The ID '${r.toolCallId}' doesn't match the active tool call ID '${e}'.`)):(e=void 0,x(r));case S.STEP_STARTED:{let p=r.name;return o.has(p)?y(()=>new A(`Step "${p}" is already active for 'STEP_STARTED'`)):(o.set(p,!0),x(r))}case S.STEP_FINISHED:{let p=r.name;return o.has(p)?(o.delete(p),x(r)):y(()=>new A(`Cannot send 'STEP_FINISHED' for step "${p}" that was not started`))}case S.RUN_STARTED:return x(r);case S.RUN_FINISHED:{if(o.size>0){let p=Array.from(o.keys()).join(", ");return y(()=>new A(`Cannot send 'RUN_FINISHED' while steps are still active: ${p}`))}return s=!0,x(r)}case S.RUN_ERROR:return i=!0,x(r);case S.CUSTOM:return x(r);default:return x(r)}}))};import{EventSchemas as ie}from"@ag-ui/core";import{Subject as le,ReplaySubject as ce}from"rxjs";import{Observable as ee,from as te,defer as ne,throwError as se}from"rxjs";import{switchMap as re}from"rxjs/operators";var D=(u,n)=>ne(()=>te(fetch(u,n))).pipe(re(t=>{var i;let e={type:"headers",status:t.status,headers:t.headers},s=(i=t.body)==null?void 0:i.getReader();return s?new ee(l=>(l.next(e),(async()=>{try{for(;;){let{done:o,value:r}=await s.read();if(o)break;let f={type:"data",data:r};l.next(f)}l.complete()}catch(o){l.error(o)}})(),()=>{s.cancel()})):se(()=>new Error("Failed to getReader() from response"))}));import{Subject as ae}from"rxjs";var H=u=>{let n=new ae,t=new TextDecoder("utf-8",{fatal:!1}),e="";u.subscribe({next:i=>{if(i.type!=="headers"&&i.type==="data"&&i.data){let l=t.decode(i.data,{stream:!0});e+=l;let o=e.split(/\n\n/);e=o.pop()||"";for(let r of o)s(r)}},error:i=>n.error(i),complete:()=>{e&&(e+=t.decode(),s(e)),n.complete()}});function s(i){let l=i.split(`
`),o=[];for(let r of l)r.startsWith("data: ")&&o.push(r.slice(6));if(o.length>0)try{let r=o.join(`
`),f=JSON.parse(r);n.next(f)}catch(r){n.error(r)}}return n.asObservable()};import{Subject as oe}from"rxjs";import*as J from"@ag-ui/proto";var P=u=>{let n=new oe,t=new Uint8Array(0);u.subscribe({next:s=>{if(s.type!=="headers"&&s.type==="data"&&s.data){let i=new Uint8Array(t.length+s.data.length);i.set(t,0),i.set(s.data,t.length),t=i,e()}},error:s=>n.error(s),complete:()=>{if(t.length>0)try{e()}catch(s){console.warn("Incomplete or invalid protocol buffer data at stream end")}n.complete()}});function e(){for(;t.length>=4;){let l=4+new DataView(t.buffer,t.byteOffset,4).getUint32(0,!1);if(t.length<l)break;try{let o=t.slice(4,l),r=J.decode(o);n.next(r),t=t.slice(l)}catch(o){let r=o instanceof Error?o.message:String(o);n.error(new Error(`Failed to decode protocol buffer message: ${r}`));return}}}return n.asObservable()};import*as z from"@ag-ui/proto";var G=u=>{let n=new le,t=new ce,e=!1;return u.subscribe({next:s=>{t.next(s),s.type==="headers"&&!e?(e=!0,s.headers.get("content-type")===z.AGUI_MEDIA_TYPE?P(t).subscribe({next:l=>n.next(l),error:l=>n.error(l),complete:()=>n.complete()}):H(t).subscribe({next:l=>{try{let o=ie.parse(l);n.next(o)}catch(o){n.error(o)}},error:l=>n.error(l),complete:()=>n.complete()})):e||n.error(new Error("No headers event received before data events"))},error:s=>{t.error(s),n.error(s)},complete:()=>{t.complete()}}),n.asObservable()};import{mergeMap as ye}from"rxjs/operators";import{applyPatch as ve}from"fast-json-patch";import{EventType as C}from"@ag-ui/core";import{z as a}from"zod";var T=a.enum(["TextMessageStart","TextMessageContent","TextMessageEnd","ActionExecutionStart","ActionExecutionArgs","ActionExecutionEnd","ActionExecutionResult","AgentStateMessage","MetaEvent","RunStarted","RunFinished","RunError","NodeStarted","NodeFinished"]),ue=a.enum(["LangGraphInterruptEvent","PredictState","Exit"]),ge=a.object({type:a.literal(T.enum.TextMessageStart),messageId:a.string(),parentMessageId:a.string().optional()}),Ee=a.object({type:a.literal(T.enum.TextMessageContent),messageId:a.string(),content:a.string()}),pe=a.object({type:a.literal(T.enum.TextMessageEnd),messageId:a.string()}),de=a.object({type:a.literal(T.enum.ActionExecutionStart),actionExecutionId:a.string(),actionName:a.string(),parentMessageId:a.string().optional()}),fe=a.object({type:a.literal(T.enum.ActionExecutionArgs),actionExecutionId:a.string(),args:a.string()}),Te=a.object({type:a.literal(T.enum.ActionExecutionEnd),actionExecutionId:a.string()}),me=a.object({type:a.literal(T.enum.ActionExecutionResult),actionName:a.string(),actionExecutionId:a.string(),result:a.string()}),Se=a.object({type:a.literal(T.enum.AgentStateMessage),threadId:a.string(),agentName:a.string(),nodeName:a.string(),runId:a.string(),active:a.boolean(),role:a.string(),state:a.string(),running:a.boolean()}),Ae=a.object({type:a.literal(T.enum.MetaEvent),name:ue,value:a.any()}),Ht=a.discriminatedUnion("type",[ge,Ee,pe,de,fe,Te,me,Se,Ae]),Pt=a.object({id:a.string(),role:a.string(),content:a.string(),parentMessageId:a.string().optional()}),Gt=a.object({id:a.string(),name:a.string(),arguments:a.any(),parentMessageId:a.string().optional()}),Ut=a.object({id:a.string(),result:a.any(),actionExecutionId:a.string(),actionName:a.string()});import Ce from"untruncate-json";var U=(u,n,t)=>e=>{let s={},i=!0,l=!0,o="",r=null,f=null,p=[],d=c=>{typeof c=="object"&&c!==null&&("messages"in c&&delete c.messages,s=c)};return e.pipe(ye(c=>{switch(c.type){case C.TEXT_MESSAGE_START:{let g=c;return[{type:T.enum.TextMessageStart,messageId:g.messageId}]}case C.TEXT_MESSAGE_CONTENT:{let g=c;return[{type:T.enum.TextMessageContent,messageId:g.messageId,content:g.delta}]}case C.TEXT_MESSAGE_END:{let g=c;return[{type:T.enum.TextMessageEnd,messageId:g.messageId}]}case C.TOOL_CALL_START:{let g=c;return p.push({id:g.toolCallId,type:"function",function:{name:g.toolCallName,arguments:""}}),l=!0,[{type:T.enum.ActionExecutionStart,actionExecutionId:g.toolCallId,actionName:g.toolCallName,parentMessageId:g.parentMessageId}]}case C.TOOL_CALL_ARGS:{let g=c,E=p[p.length-1];E.function.arguments+=g.delta;let h=!1;if(f){let R=f.find(O=>O.tool==E.function.name);if(R)try{let O=JSON.parse(Ce(E.function.arguments));R.tool_argument&&R.tool_argument in O?(d(M(L({},s),{[R.state_key]:O[R.tool_argument]})),h=!0):R.tool_argument||(d(M(L({},s),{[R.state_key]:O})),h=!0)}catch(O){}}return[{type:T.enum.ActionExecutionArgs,actionExecutionId:g.toolCallId,args:g.delta},...h?[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:"assistant",state:JSON.stringify(s),active:l}]:[]]}case C.TOOL_CALL_END:{let g=c;return[{type:T.enum.ActionExecutionEnd,actionExecutionId:g.toolCallId}]}case C.RAW:return[];case C.CUSTOM:{let g=c;switch(g.name){case"Exit":i=!1;break;case"PredictState":f=g.value;break}return[{type:T.enum.MetaEvent,name:g.name,value:g.value}]}case C.STATE_SNAPSHOT:return d(c.snapshot),[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:"assistant",state:JSON.stringify(s),active:l}];case C.STATE_DELTA:{let E=ve(s,c.delta,!0,!1);return E?(d(E.newDocument),[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:"assistant",state:JSON.stringify(s),active:l}]):[]}case C.MESSAGES_SNAPSHOT:return r=c.messages,[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:"assistant",state:JSON.stringify(L(L({},s),r?{messages:r}:{})),active:!0}];case C.RUN_STARTED:return[];case C.RUN_FINISHED:return r&&(s.messages=r),[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:"assistant",state:JSON.stringify(L(L({},s),r?{messages:_e(r)}:{})),active:!1}];case C.RUN_ERROR:return console.error("Run error",c),[];case C.STEP_STARTED:return o=c.stepName,p=[],f=null,[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:"assistant",state:JSON.stringify(s),active:!0}];case C.STEP_FINISHED:return p=[],f=null,[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:"assistant",state:JSON.stringify(s),active:!1}];default:return[]}}))};function _e(u){var t;let n=[];for(let e of u)if(e.role==="assistant"||e.role==="user"||e.role==="system"){if(e.content){let s={id:e.id,role:e.role,content:e.content};n.push(s)}if(e.role==="assistant"&&e.toolCalls&&e.toolCalls.length>0)for(let s of e.toolCalls){let i={id:s.id,name:s.function.name,arguments:JSON.parse(s.function.arguments),parentMessageId:e.id};n.push(i)}}else if(e.role==="tool"){let s="unknown";for(let l of u)if(l.role==="assistant"&&((t=l.toolCalls)!=null&&t.length)){for(let o of l.toolCalls)if(o.id===e.toolCallId){s=o.function.name;break}}let i={id:e.id,result:e.content,actionExecutionId:e.toolCallId,actionName:s};n.push(i)}return n}import{v4 as I}from"uuid";import{catchError as he,map as Me,tap as Re}from"rxjs/operators";import{finalize as Oe}from"rxjs/operators";import{throwError as be,pipe as Ne}from"rxjs";import{lastValueFrom as Ie,of as we}from"rxjs";import{mergeMap as Le,finalize as xe}from"rxjs";import{EventType as m}from"@ag-ui/core";var F=u=>n=>{let t,e,s,i=()=>{if(!t||s!=="text")throw new Error("No text message to close");let r={type:m.TEXT_MESSAGE_END,messageId:t.messageId};return s=void 0,t=void 0,u&&console.debug("[TRANSFORM]: TEXT_MESSAGE_END",JSON.stringify(r)),r},l=()=>{if(!e||s!=="tool")throw new Error("No tool call to close");let r={type:m.TOOL_CALL_END,toolCallId:e.toolCallId};return s=void 0,e=void 0,u&&console.debug("[TRANSFORM]: TOOL_CALL_END",JSON.stringify(r)),r},o=()=>s==="text"?[i()]:s==="tool"?[l()]:[];return n.pipe(Le(r=>{switch(r.type){case m.TEXT_MESSAGE_START:case m.TEXT_MESSAGE_CONTENT:case m.TEXT_MESSAGE_END:case m.TOOL_CALL_START:case m.TOOL_CALL_ARGS:case m.TOOL_CALL_END:case m.STATE_SNAPSHOT:case m.STATE_DELTA:case m.MESSAGES_SNAPSHOT:case m.CUSTOM:case m.RUN_STARTED:case m.RUN_FINISHED:case m.RUN_ERROR:case m.STEP_STARTED:case m.STEP_FINISHED:return[...o(),r];case m.RAW:return[r];case m.TEXT_MESSAGE_CHUNK:let p=r,d=[];if((s!=="text"||p.messageId!==void 0&&p.messageId!==(t==null?void 0:t.messageId))&&d.push(...o()),s!=="text"){if(p.messageId===void 0)throw new Error("First TEXT_MESSAGE_CHUNK must have a messageId");t={messageId:p.messageId},s="text";let E={type:m.TEXT_MESSAGE_START,messageId:p.messageId,role:"assistant"};d.push(E),u&&console.debug("[TRANSFORM]: TEXT_MESSAGE_START",JSON.stringify(E))}if(p.delta!==void 0){let E={type:m.TEXT_MESSAGE_CONTENT,messageId:t.messageId,delta:p.delta};d.push(E),u&&console.debug("[TRANSFORM]: TEXT_MESSAGE_CONTENT",JSON.stringify(E))}return d;case m.TOOL_CALL_CHUNK:let c=r,g=[];if((s!=="tool"||c.toolCallId!==void 0&&c.toolCallId!==(e==null?void 0:e.toolCallId))&&g.push(...o()),s!=="tool"){if(c.toolCallId===void 0)throw new Error("First TOOL_CALL_CHUNK must have a toolCallId");if(c.toolCallName===void 0)throw new Error("First TOOL_CALL_CHUNK must have a toolCallName");e={toolCallId:c.toolCallId,toolCallName:c.toolCallName,parentMessageId:c.parentMessageId},s="tool";let E={type:m.TOOL_CALL_START,toolCallId:c.toolCallId,toolCallName:c.toolCallName,parentMessageId:c.parentMessageId};g.push(E),u&&console.debug("[TRANSFORM]: TOOL_CALL_START",JSON.stringify(E))}if(c.delta!==void 0){let E={type:m.TOOL_CALL_ARGS,toolCallId:e.toolCallId,delta:c.delta};g.push(E),u&&console.debug("[TRANSFORM]: TOOL_CALL_ARGS",JSON.stringify(E))}return g}let f=r.type}),xe(()=>o()))};var b=class{constructor({agentId:n,description:t,threadId:e,initialMessages:s,initialState:i,debug:l}={}){this.debug=!1;this.agentId=n,this.description=t!=null?t:"",this.threadId=e!=null?e:I(),this.messages=_(s!=null?s:[]),this.state=_(i!=null?i:{}),this.debug=l!=null?l:!1}async runAgent(n){var s;this.agentId=(s=this.agentId)!=null?s:I();let t=this.prepareRunAgentInput(n),e=Ne(()=>this.run(t),F(this.debug),N(this.debug),i=>this.apply(t,i),i=>this.processApplyEvents(t,i),he(i=>(this.onError(i),be(()=>i))),Oe(()=>{this.onFinalize()}));return Ie(e(we(null))).then(()=>{})}abortRun(){}apply(...n){return w(...n)}processApplyEvents(n,t){return t.pipe(Re(e=>{e.messages&&(this.messages=e.messages),e.state&&(this.state=e.state)}))}prepareRunAgentInput(n){var t,e,s;return{threadId:this.threadId,runId:(n==null?void 0:n.runId)||I(),tools:_((t=n==null?void 0:n.tools)!=null?t:[]),context:_((e=n==null?void 0:n.context)!=null?e:[]),forwardedProps:_((s=n==null?void 0:n.forwardedProps)!=null?s:{}),state:_(this.state),messages:_(this.messages)}}onError(n){console.error("Agent execution failed:",n)}onFinalize(){}clone(){let n=Object.create(Object.getPrototypeOf(this));for(let t of Object.getOwnPropertyNames(this)){let e=this[t];typeof e!="function"&&(n[t]=_(e))}return n}legacy_to_be_removed_runAgentBridged(n){var e;this.agentId=(e=this.agentId)!=null?e:I();let t=this.prepareRunAgentInput(n);return this.run(t).pipe(F(this.debug),N(this.debug),U(this.threadId,t.runId,this.agentId),s=>s.pipe(Me(i=>(this.debug&&console.debug("[LEGACY]:",JSON.stringify(i)),i))))}};var X=class extends b{constructor(t){var e;super(t);this.abortController=new AbortController;this.url=t.url,this.headers=_((e=t.headers)!=null?e:{})}requestInit(t){return{method:"POST",headers:M(L({},this.headers),{"Content-Type":"application/json",Accept:"text/event-stream"}),body:JSON.stringify(t),signal:this.abortController.signal}}runAgent(t){var e;return this.abortController=(e=t==null?void 0:t.abortController)!=null?e:new AbortController,super.runAgent(t)}abortRun(){this.abortController.abort(),super.abortRun()}run(t){let e=D(this.url,this.requestInit(t));return G(e)}};export*from"@ag-ui/core";export{b as AbstractAgent,X as HttpAgent,U as convertToLegacyEvents,w as defaultApplyEvents,P as parseProtoStream,H as parseSSEStream,D as runHttpRequest,G as transformHttpEventStream,N as verifyEvents};
//# sourceMappingURL=index.mjs.map
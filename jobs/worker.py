"""
Celery worker module.

This module initializes the Celery application and configures it
for processing asynchronous tasks.
"""

import os
import logging
from celery import Celery
from dotenv import load_dotenv

# Load environment variables from .env.jobs file if it exists
env_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env.jobs')
if os.path.exists(env_file):
    load_dotenv(env_file)

# Import configuration
from jobs.config import get_celery_config

# Configure logging
logging.basicConfig(
    level=os.getenv('LOG_LEVEL', 'INFO'),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

# Create Celery application
app = Celery('jobs')

# Configure Celery
app.conf.update(get_celery_config())

# Auto-discover tasks in the tasks directory
app.autodiscover_tasks(['jobs.tasks.document_tasks',
                        'jobs.tasks.embedding_tasks',
                        'jobs.tasks.notification_tasks',
                        'jobs.tasks.deadline_insights_tasks'])

# Register signals
@app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """
    Set up periodic tasks.

    Args:
        sender: The Celery app instance
        **kwargs: Additional arguments
    """
    # Import deadline insights tasks
    from jobs.tasks.deadline_insights_tasks import generate_periodic_deadline_insights

    # Schedule deadline insights generation every 4 hours
    sender.add_periodic_task(
        14400.0,  # 4 hours in seconds
        generate_periodic_deadline_insights.s(),
        name='generate deadline insights every 4 hours',
    )

    # Schedule morning deadline briefings at 8 AM daily
    from celery.schedules import crontab
    sender.add_periodic_task(
        crontab(hour=8, minute=0),  # 8:00 AM daily
        generate_periodic_deadline_insights.s(),
        name='morning deadline briefing',
    )

    logger.info("Periodic tasks configured: deadline insights every 4 hours and morning briefings")

@app.task
def cleanup_expired_jobs():
    """
    Clean up expired jobs from the result backend.
    """
    logger.info("Cleaning up expired jobs")
    # Implementation goes here
    return True

if __name__ == '__main__':
    app.start()

"""
Helper functions for job management.

This module provides utility functions for submitting jobs to Celery
and checking their status.
"""

import logging
import uuid
from typing import Dict, Any, Optional, Union
from datetime import datetime

from celery.result import AsyncResult

# Import Celery app and tasks
from jobs.worker import app
from jobs.tasks.embedding_tasks import generate_document_embedding
from jobs.tasks.document_tasks import process_document
from jobs.tasks.notification_tasks import send_email_notification, send_deadline_alert, send_user_return_alert
from jobs.tasks.deadline_insights_tasks import generate_periodic_deadline_insights, generate_user_return_insights

# Configure logging
logger = logging.getLogger(__name__)

def submit_embedding_job(document_id: str,
                        tenant_id: str,
                        user_id: str,
                        case_id: Optional[str] = None,
                        client_id: Optional[str] = None,
                        metadata: Optional[Dict[str, Any]] = None,
                        priority: Optional[int] = 0) -> str:
    """
    Submit a document for embedding generation.
    
    Args:
        document_id: Unique identifier for the document
        tenant_id: Tenant identifier
        user_id: User identifier
        case_id: Optional case identifier
        client_id: Optional client identifier
        metadata: Additional metadata for processing
        priority: Task priority (0-9, higher is more important)
        
    Returns:
        str: Job ID for tracking the task
    """
    try:
        logger.info(f"Submitting embedding job for document {document_id}")
        
        # Submit the task to Celery
        task = generate_document_embedding.apply_async(
            args=[document_id, tenant_id, user_id, case_id, client_id, metadata, priority],
            priority=priority
        )
        
        job_id = task.id
        logger.info(f"Embedding job submitted with ID {job_id}")
        
        return job_id
    
    except Exception as e:
        logger.error(f"Error submitting embedding job: {str(e)}")
        raise

def submit_document_processing_job(document_id: str,
                                  tenant_id: str,
                                  user_id: str,
                                  file_path: str,
                                  metadata: Optional[Dict[str, Any]] = None) -> str:
    """
    Submit a document for processing.
    
    Args:
        document_id: Unique identifier for the document
        tenant_id: Tenant identifier
        user_id: User identifier
        file_path: Path to the document file
        metadata: Additional metadata for processing
        
    Returns:
        str: Job ID for tracking the task
    """
    try:
        logger.info(f"Submitting document processing job for document {document_id}")
        
        # Submit the task to Celery
        task = process_document.apply_async(
            args=[document_id, tenant_id, user_id, file_path, metadata]
        )
        
        job_id = task.id
        logger.info(f"Document processing job submitted with ID {job_id}")
        
        return job_id
    
    except Exception as e:
        logger.error(f"Error submitting document processing job: {str(e)}")
        raise

def submit_email_notification_job(recipient_email: str,
                                 subject: str,
                                 message: str,
                                 tenant_id: str,
                                 user_id: Optional[str] = None,
                                 template_id: Optional[str] = None,
                                 template_data: Optional[Dict[str, Any]] = None) -> str:
    """
    Submit an email notification job.
    
    Args:
        recipient_email: Email address of the recipient
        subject: Email subject
        message: Email message body
        tenant_id: Tenant identifier
        user_id: Optional user identifier
        template_id: Optional email template identifier
        template_data: Optional template data for rendering
        
    Returns:
        str: Job ID for tracking the task
    """
    try:
        logger.info(f"Submitting email notification job to {recipient_email}")
        
        # Submit the task to Celery
        task = send_email_notification.apply_async(
            args=[recipient_email, subject, message, tenant_id, user_id, template_id, template_data]
        )
        
        job_id = task.id
        logger.info(f"Email notification job submitted with ID {job_id}")
        
        return job_id
    
    except Exception as e:
        logger.error(f"Error submitting email notification job: {str(e)}")
        raise

def check_job_status(job_id: str) -> Dict[str, Any]:
    """
    Check the status of a job.
    
    Args:
        job_id: Job ID to check
        
    Returns:
        Dict[str, Any]: Job status information
    """
    try:
        logger.info(f"Checking status for job {job_id}")
        
        # Get the task result
        result = AsyncResult(job_id, app=app)
        
        # Build status response
        status_info = {
            "job_id": job_id,
            "status": result.status,
            "checked_at": datetime.now().isoformat(),
        }
        
        # Add result if available
        if result.ready():
            if result.successful():
                status_info["result"] = result.get()
            else:
                status_info["error"] = str(result.result)
                status_info["traceback"] = result.traceback
        
        logger.info(f"Job {job_id} status: {result.status}")
        return status_info
    
    except Exception as e:
        logger.error(f"Error checking job status: {str(e)}")
        return {
            "job_id": job_id,
            "status": "error",
            "error": str(e),
            "checked_at": datetime.now().isoformat(),
        }


def submit_deadline_insights_job(tenant_ids: Optional[List[str]] = None) -> str:
    """
    Submit a deadline insights generation job.

    Args:
        tenant_ids: Optional list of specific tenant IDs to process

    Returns:
        str: Job ID for tracking the task
    """
    try:
        logger.info(f"Submitting deadline insights job for tenants: {tenant_ids or 'all'}")

        # Submit the task to Celery
        task = generate_periodic_deadline_insights.apply_async(
            args=[tenant_ids]
        )

        job_id = task.id
        logger.info(f"Deadline insights job submitted with ID {job_id}")

        return job_id

    except Exception as e:
        logger.error(f"Error submitting deadline insights job: {str(e)}")
        raise


def submit_user_return_insights_job(user_id: str, tenant_id: str, last_activity_time: str) -> str:
    """
    Submit a user return insights generation job.

    Args:
        user_id: ID of the returning user
        tenant_id: Tenant ID for the user
        last_activity_time: ISO timestamp of user's last activity

    Returns:
        str: Job ID for tracking the task
    """
    try:
        logger.info(f"Submitting user return insights job for user {user_id}")

        # Submit the task to Celery
        task = generate_user_return_insights.apply_async(
            args=[user_id, tenant_id, last_activity_time]
        )

        job_id = task.id
        logger.info(f"User return insights job submitted with ID {job_id}")

        return job_id

    except Exception as e:
        logger.error(f"Error submitting user return insights job: {str(e)}")
        raise


def submit_deadline_alert_job(tenant_id: str, deadline_data: Dict[str, Any], alert_type: str = 'critical_deadline') -> str:
    """
    Submit a deadline alert notification job.

    Args:
        tenant_id: Tenant identifier
        deadline_data: Deadline information and recommendations
        alert_type: Type of alert (critical_deadline, conflict, etc.)

    Returns:
        str: Job ID for tracking the task
    """
    try:
        logger.info(f"Submitting deadline alert job for tenant {tenant_id}")

        # Submit the task to Celery
        task = send_deadline_alert.apply_async(
            args=[tenant_id, deadline_data, alert_type]
        )

        job_id = task.id
        logger.info(f"Deadline alert job submitted with ID {job_id}")

        return job_id

    except Exception as e:
        logger.error(f"Error submitting deadline alert job: {str(e)}")
        raise


def trigger_user_return_insights(user_id: str, tenant_id: str, last_activity_time: str) -> Optional[str]:
    """
    Trigger user return insights if the user has been away long enough.

    Args:
        user_id: ID of the returning user
        tenant_id: Tenant ID for the user
        last_activity_time: ISO timestamp of user's last activity

    Returns:
        Optional[str]: Job ID if insights were triggered, None if skipped
    """
    try:
        from datetime import datetime, timezone

        # Parse last activity time
        last_activity = datetime.fromisoformat(last_activity_time.replace('Z', '+00:00'))
        time_away = datetime.now(timezone.utc) - last_activity

        # Only trigger if user was away for more than 1 hour
        if time_away.total_seconds() < 3600:  # 1 hour
            logger.info(f"User {user_id} was only away for {time_away.total_seconds()/60:.1f} minutes, skipping insights")
            return None

        # Submit the job
        job_id = submit_user_return_insights_job(user_id, tenant_id, last_activity_time)
        logger.info(f"Triggered user return insights for user {user_id} (away {time_away.total_seconds()/3600:.1f} hours)")

        return job_id

    except Exception as e:
        logger.error(f"Error triggering user return insights: {str(e)}")
        return None

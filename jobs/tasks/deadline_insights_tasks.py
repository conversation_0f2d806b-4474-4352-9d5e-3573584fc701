"""
Deadline Insights Tasks for Celery

This module defines Celery tasks for automated deadline insights generation,
including periodic analysis, user return triggers, and proactive notifications.
"""

import logging
import os
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, timezone

from celery import shared_task
from celery.exceptions import Retry

# Configure logging
logger = logging.getLogger(__name__)

# Retry configuration
MAX_RETRIES = int(os.getenv("PROCESSING_MAX_RETRIES", "3"))
RETRY_DELAY = int(os.getenv("PROCESSING_RETRY_DELAY", "5"))  # seconds


@shared_task(bind=True, 
             name="jobs.tasks.deadline_insights_tasks.generate_periodic_deadline_insights",
             max_retries=MAX_RETRIES,
             default_retry_delay=RETRY_DELAY)
def generate_periodic_deadline_insights(self, tenant_ids: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Generate deadline insights for all tenants or specified tenants.
    
    This task runs periodically (every 4 hours) to analyze deadlines,
    detect conflicts, assess risks, and generate proactive recommendations.
    
    Args:
        self: Task instance (injected by Celery)
        tenant_ids: Optional list of specific tenant IDs to process
        
    Returns:
        Dict containing processing results and statistics
    """
    start_time = time.time()
    results = {
        'task_id': self.request.id,
        'started_at': datetime.now(timezone.utc).isoformat(),
        'tenant_ids_processed': [],
        'insights_generated': 0,
        'errors': [],
        'processing_time_seconds': 0,
        'status': 'started'
    }
    
    try:
        logger.info(f"Starting periodic deadline insights generation (task_id: {self.request.id})")
        
        # Import here to avoid circular imports
        from backend.agents.insights.deadline import DeadlineInsightsAgent, DeadlineInsightsState, AnalysisType
        from backend.db.session import get_supabase_client
        
        # Get Supabase client
        supabase = get_supabase_client(use_service_role=True)
        
        # Get all active tenants if not specified
        if not tenant_ids:
            tenant_response = supabase.from('tenants').select('id').eq('status', 'active').execute()
            if tenant_response.data:
                tenant_ids = [tenant['id'] for tenant in tenant_response.data]
            else:
                logger.warning("No active tenants found")
                results['status'] = 'completed'
                results['processing_time_seconds'] = time.time() - start_time
                return results
        
        # Initialize deadline insights agent
        agent = DeadlineInsightsAgent()
        
        # Process each tenant
        for tenant_id in tenant_ids:
            try:
                logger.info(f"Processing deadline insights for tenant: {tenant_id}")
                
                # Create state for comprehensive analysis
                state = DeadlineInsightsState(
                    tenant_id=tenant_id,
                    analysis_type=AnalysisType.COMPREHENSIVE,
                    matter_ids=None,  # Analyze all matters for the tenant
                    batch_size=50,
                    cache_enabled=True
                )
                
                # Execute the agent
                result_state = await agent.execute(state)
                
                if result_state.status == 'completed' and not result_state.error:
                    results['tenant_ids_processed'].append(tenant_id)
                    
                    # Count insights generated
                    if result_state.analysis_results:
                        insights_count = len(result_state.analysis_results.get('recommendations', []))
                        results['insights_generated'] += insights_count
                        logger.info(f"Generated {insights_count} insights for tenant {tenant_id}")
                        
                        # Store insights in database for dashboard display
                        await store_deadline_insights(tenant_id, result_state.analysis_results)
                        
                        # Trigger notifications for critical insights
                        await trigger_deadline_notifications(tenant_id, result_state.analysis_results)
                    
                else:
                    error_msg = f"Failed to generate insights for tenant {tenant_id}: {result_state.error}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
                    
            except Exception as tenant_error:
                error_msg = f"Error processing tenant {tenant_id}: {str(tenant_error)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)
                continue
        
        # Update final results
        results['status'] = 'completed' if not results['errors'] else 'completed_with_errors'
        results['processing_time_seconds'] = time.time() - start_time
        results['completed_at'] = datetime.now(timezone.utc).isoformat()
        
        logger.info(f"Periodic deadline insights generation completed. "
                   f"Processed {len(results['tenant_ids_processed'])} tenants, "
                   f"Generated {results['insights_generated']} insights, "
                   f"Errors: {len(results['errors'])}")
        
        return results
        
    except Exception as e:
        error_msg = f"Fatal error in periodic deadline insights generation: {str(e)}"
        logger.error(error_msg)
        results['status'] = 'failed'
        results['error'] = error_msg
        results['processing_time_seconds'] = time.time() - start_time
        
        # Retry on failure
        if self.request.retries < MAX_RETRIES:
            logger.info(f"Retrying task in {RETRY_DELAY} seconds (attempt {self.request.retries + 1}/{MAX_RETRIES})")
            raise self.retry(countdown=RETRY_DELAY, exc=e)
        
        return results


@shared_task(bind=True,
             name="jobs.tasks.deadline_insights_tasks.generate_user_return_insights",
             max_retries=MAX_RETRIES,
             default_retry_delay=RETRY_DELAY)
def generate_user_return_insights(self, user_id: str, tenant_id: str, last_activity_time: str) -> Dict[str, Any]:
    """
    Generate deadline insights when a user returns after 1+ hours.
    
    This task provides fresh insights when users re-engage with the platform,
    focusing on changes since their last activity.
    
    Args:
        self: Task instance (injected by Celery)
        user_id: ID of the returning user
        tenant_id: Tenant ID for the user
        last_activity_time: ISO timestamp of user's last activity
        
    Returns:
        Dict containing insights and processing results
    """
    start_time = time.time()
    results = {
        'task_id': self.request.id,
        'user_id': user_id,
        'tenant_id': tenant_id,
        'last_activity_time': last_activity_time,
        'started_at': datetime.now(timezone.utc).isoformat(),
        'insights_generated': 0,
        'status': 'started'
    }
    
    try:
        logger.info(f"Generating user return insights for user {user_id} in tenant {tenant_id}")
        
        # Import here to avoid circular imports
        from backend.agents.insights.deadline import DeadlineInsightsAgent, DeadlineInsightsState, AnalysisType
        
        # Parse last activity time
        last_activity = datetime.fromisoformat(last_activity_time.replace('Z', '+00:00'))
        time_away = datetime.now(timezone.utc) - last_activity
        
        # Only generate insights if user was away for more than 1 hour
        if time_away.total_seconds() < 3600:  # 1 hour
            logger.info(f"User {user_id} was only away for {time_away.total_seconds()/60:.1f} minutes, skipping insights")
            results['status'] = 'skipped'
            results['reason'] = 'insufficient_time_away'
            return results
        
        # Get user's matters for focused analysis
        from backend.db.session import get_supabase_client
        supabase = get_supabase_client(use_service_role=True)
        
        # Get matters assigned to or created by the user
        matters_response = supabase.schema('tenants').from('matters').select('id').or_(
            f'assigned_to.eq.{user_id},created_by.eq.{user_id}'
        ).eq('tenant_id', tenant_id).execute()
        
        matter_ids = [matter['id'] for matter in matters_response.data] if matters_response.data else []
        
        if not matter_ids:
            logger.info(f"No matters found for user {user_id}, generating tenant-wide insights")
            matter_ids = None
        
        # Initialize deadline insights agent
        agent = DeadlineInsightsAgent()
        
        # Create state for user-focused analysis
        state = DeadlineInsightsState(
            tenant_id=tenant_id,
            analysis_type=AnalysisType.COMPREHENSIVE,
            matter_ids=matter_ids,
            batch_size=25,  # Smaller batch for user-specific analysis
            cache_enabled=True
        )
        
        # Execute the agent
        result_state = await agent.execute(state)
        
        if result_state.status == 'completed' and not result_state.error:
            # Count insights generated
            if result_state.analysis_results:
                insights_count = len(result_state.analysis_results.get('recommendations', []))
                results['insights_generated'] = insights_count
                
                # Store user-specific insights
                await store_user_deadline_insights(user_id, tenant_id, result_state.analysis_results)
                
                # Send personalized notifications
                await send_user_return_notifications(user_id, tenant_id, result_state.analysis_results, time_away)
                
                logger.info(f"Generated {insights_count} return insights for user {user_id}")
            
            results['status'] = 'completed'
        else:
            results['status'] = 'failed'
            results['error'] = result_state.error
        
        results['processing_time_seconds'] = time.time() - start_time
        results['completed_at'] = datetime.now(timezone.utc).isoformat()
        
        return results
        
    except Exception as e:
        error_msg = f"Error generating user return insights: {str(e)}"
        logger.error(error_msg)
        results['status'] = 'failed'
        results['error'] = error_msg
        results['processing_time_seconds'] = time.time() - start_time
        
        # Retry on failure
        if self.request.retries < MAX_RETRIES:
            logger.info(f"Retrying user return insights in {RETRY_DELAY} seconds")
            raise self.retry(countdown=RETRY_DELAY, exc=e)
        
        return results


async def store_deadline_insights(tenant_id: str, analysis_results: Dict[str, Any]) -> None:
    """Store deadline insights in database for dashboard display."""
    try:
        from backend.db.session import get_supabase_client
        
        supabase = get_supabase_client(use_service_role=True)
        
        # Store insights in tenant schema
        insights_data = {
            'tenant_id': tenant_id,
            'analysis_type': 'comprehensive',
            'insights': analysis_results,
            'generated_at': datetime.now(timezone.utc).isoformat(),
            'expires_at': (datetime.now(timezone.utc) + timedelta(hours=4)).isoformat()
        }
        
        # Upsert insights (replace existing if any)
        supabase.schema('tenants').from('deadline_insights').upsert(
            insights_data, 
            on_conflict='tenant_id'
        ).execute()
        
        logger.info(f"Stored deadline insights for tenant {tenant_id}")
        
    except Exception as e:
        logger.error(f"Error storing deadline insights for tenant {tenant_id}: {e}")


async def store_user_deadline_insights(user_id: str, tenant_id: str, analysis_results: Dict[str, Any]) -> None:
    """Store user-specific deadline insights."""
    try:
        from backend.db.session import get_supabase_client
        
        supabase = get_supabase_client(use_service_role=True)
        
        insights_data = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'analysis_type': 'user_return',
            'insights': analysis_results,
            'generated_at': datetime.now(timezone.utc).isoformat(),
            'expires_at': (datetime.now(timezone.utc) + timedelta(hours=8)).isoformat()
        }
        
        supabase.schema('tenants').from('user_deadline_insights').upsert(
            insights_data,
            on_conflict='user_id,tenant_id'
        ).execute()
        
        logger.info(f"Stored user deadline insights for user {user_id}")
        
    except Exception as e:
        logger.error(f"Error storing user deadline insights: {e}")


async def trigger_deadline_notifications(tenant_id: str, analysis_results: Dict[str, Any]) -> None:
    """Trigger notifications for critical deadline insights."""
    try:
        # Import notification tasks
        from jobs.tasks.notification_tasks import send_deadline_alert
        
        # Check for critical deadlines
        critical_deadlines = []
        if 'recommendations' in analysis_results:
            for rec in analysis_results['recommendations']:
                if rec.get('priority') in ['CRITICAL', 'HIGH']:
                    critical_deadlines.append(rec)
        
        # Send alerts for critical deadlines
        for deadline in critical_deadlines:
            send_deadline_alert.delay(
                tenant_id=tenant_id,
                deadline_data=deadline,
                alert_type='critical_deadline'
            )
        
        logger.info(f"Triggered {len(critical_deadlines)} deadline notifications for tenant {tenant_id}")
        
    except Exception as e:
        logger.error(f"Error triggering deadline notifications: {e}")


async def send_user_return_notifications(user_id: str, tenant_id: str, analysis_results: Dict[str, Any], time_away: timedelta) -> None:
    """Send personalized notifications for returning users."""
    try:
        from jobs.tasks.notification_tasks import send_user_return_alert
        
        # Create personalized message based on time away
        hours_away = int(time_away.total_seconds() / 3600)
        
        send_user_return_alert.delay(
            user_id=user_id,
            tenant_id=tenant_id,
            insights=analysis_results,
            hours_away=hours_away
        )
        
        logger.info(f"Sent return notification to user {user_id} (away {hours_away} hours)")
        
    except Exception as e:
        logger.error(f"Error sending user return notification: {e}")

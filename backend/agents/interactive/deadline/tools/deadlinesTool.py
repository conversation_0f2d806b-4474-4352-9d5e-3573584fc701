"""
Deadlines Tool for MCP Rules Engine Integration

This tool provides deadline calculation functionality by integrating with the MCP Rules Engine.
It validates input parameters and returns structured deadline information.

This is the Python equivalent of the TypeScript deadlinesTool.ts file.
"""

import os
import logging
from typing import List, Optional
from datetime import datetime
from dataclasses import dataclass

# Set up logging
logger = logging.getLogger(__name__)


@dataclass
class DeadlinesInput:
    """Input parameters for deadline calculation."""
    jurisdiction: str
    triggerCode: str
    startDate: str
    practiceArea: str = "personal_injury"


@dataclass
class DeadlineItem:
    """Individual deadline item."""
    id: str
    name: str
    dueDate: str
    priority: str  # 'low', 'medium', 'high'
    category: str
    description: str
    legalBasis: str
    consequences: str


@dataclass
class DeadlinesOutput:
    """Output from deadline calculation."""
    deadlines: List[DeadlineItem]
    jurisdiction: str
    triggerCode: str
    startDate: str
    practiceArea: str
    calculatedAt: str
    source: str


class DeadlinesTool:
    """Tool for calculating legal deadlines using the MCP Rules Engine."""
    
    def __init__(self):
        self.name = "deadlines_tool"
        self.description = "Calculate legal deadlines based on jurisdiction, trigger event, and date"
        
    async def invoke(self, input_data: DeadlinesInput) -> DeadlinesOutput:
        """
        Calculate deadlines using the MCP Rules Engine.
        
        Args:
            input_data: Deadline calculation parameters
            
        Returns:
            Calculated deadlines and metadata
        """
        try:
            logger.info(f"Calculating deadlines for {input_data.jurisdiction} - {input_data.triggerCode}")
            
            # Check if MCP Rules Engine is enabled
            if os.getenv('FEATURE_MCP_RULES_ENGINE') != 'true':
                logger.info("MCP Rules Engine feature flag is disabled, returning mock data")
                return self._create_mock_response(input_data)
            
            # In a real implementation, this would call the MCP Rules Engine
            # For now, return mock data
            return self._create_mock_response(input_data)
            
        except Exception as e:
            logger.error(f"Error calculating deadlines: {e}")
            # Return empty result on error
            return DeadlinesOutput(
                deadlines=[],
                jurisdiction=input_data.jurisdiction,
                triggerCode=input_data.triggerCode,
                startDate=input_data.startDate,
                practiceArea=input_data.practiceArea,
                calculatedAt=datetime.now().isoformat(),
                source="error"
            )
    
    def _create_mock_response(self, input_data: DeadlinesInput) -> DeadlinesOutput:
        """Create a mock response for testing purposes."""
        
        # Create mock deadlines based on trigger code
        mock_deadlines = []
        
        if input_data.triggerCode == "SERVICE_OF_PROCESS":
            mock_deadlines = [
                DeadlineItem(
                    id="mock-1",
                    name="Answer to Complaint",
                    dueDate="2025-08-10",
                    priority="high",
                    category="pleading",
                    description="File answer to complaint within 30 days of service",
                    legalBasis="Texas Rules of Civil Procedure Rule 99",
                    consequences="Default judgment may be entered if answer not filed"
                ),
                DeadlineItem(
                    id="mock-2", 
                    name="Discovery Deadline",
                    dueDate="2025-12-10",
                    priority="medium",
                    category="discovery",
                    description="Complete discovery within 180 days",
                    legalBasis="Texas Rules of Civil Procedure Rule 190",
                    consequences="May need court permission to extend discovery"
                )
            ]
        elif input_data.triggerCode == "ACCIDENT_DATE":
            mock_deadlines = [
                DeadlineItem(
                    id="mock-3",
                    name="Statute of Limitations",
                    dueDate="2027-07-12",
                    priority="high", 
                    category="filing",
                    description="File lawsuit within 2 years of accident date",
                    legalBasis="Texas Civil Practice and Remedies Code § 16.003",
                    consequences="Claim will be barred if not filed within statute of limitations"
                )
            ]
        else:
            # Generic deadline for other trigger codes
            mock_deadlines = [
                DeadlineItem(
                    id="mock-generic",
                    name="Generic Response Deadline",
                    dueDate="2025-08-12",
                    priority="medium",
                    category="response",
                    description="Respond to legal action within 30 days",
                    legalBasis="General legal practice",
                    consequences="May result in adverse consequences if not timely responded"
                )
            ]
        
        return DeadlinesOutput(
            deadlines=mock_deadlines,
            jurisdiction=input_data.jurisdiction,
            triggerCode=input_data.triggerCode,
            startDate=input_data.startDate,
            practiceArea=input_data.practiceArea,
            calculatedAt=datetime.now().isoformat(),
            source="mock_implementation"
        )


# Create a global instance of the tool
deadlines_tool = DeadlinesTool()


def validate_deadlines_input(data: dict) -> DeadlinesInput:
    """
    Validate and convert input data to DeadlinesInput.
    
    Args:
        data: Raw input data dictionary
        
    Returns:
        Validated DeadlinesInput object
        
    Raises:
        ValueError: If validation fails
    """
    required_fields = ['jurisdiction', 'triggerCode', 'startDate']
    
    for field in required_fields:
        if field not in data or not data[field]:
            raise ValueError(f"Missing required field: {field}")
    
    # Validate date format
    start_date = data['startDate']
    try:
        datetime.strptime(start_date, '%Y-%m-%d')
    except ValueError:
        raise ValueError("startDate must be in YYYY-MM-DD format")
    
    return DeadlinesInput(
        jurisdiction=data['jurisdiction'],
        triggerCode=data['triggerCode'],
        startDate=start_date,
        practiceArea=data.get('practiceArea', 'personal_injury')
    )

-- Migration: Create deadline insights tables
-- Description: Create tables for storing deadline insights and user-specific insights
-- Date: 2025-07-12

-- Create deadline_insights table in tenants schema
CREATE TABLE IF NOT EXISTS tenants.deadline_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL DEFAULT 'comprehensive',
    insights JSONB NOT NULL,
    generated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Ensure only one active insight per tenant per analysis type
    UNIQUE(tenant_id, analysis_type)
);

-- Create user_deadline_insights table in tenants schema
CREATE TABLE IF NOT EXISTS tenants.user_deadline_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL DEFAULT 'user_return',
    insights JSONB NOT NULL,
    generated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Ensure only one active insight per user per tenant
    UNIQUE(user_id, tenant_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_deadline_insights_tenant_id ON tenants.deadline_insights(tenant_id);
CREATE INDEX IF NOT EXISTS idx_deadline_insights_generated_at ON tenants.deadline_insights(generated_at DESC);
CREATE INDEX IF NOT EXISTS idx_deadline_insights_expires_at ON tenants.deadline_insights(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_deadline_insights_user_tenant ON tenants.user_deadline_insights(user_id, tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_deadline_insights_generated_at ON tenants.user_deadline_insights(generated_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_deadline_insights_expires_at ON tenants.user_deadline_insights(expires_at);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION tenants.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_deadline_insights_updated_at 
    BEFORE UPDATE ON tenants.deadline_insights 
    FOR EACH ROW EXECUTE FUNCTION tenants.update_updated_at_column();

CREATE TRIGGER update_user_deadline_insights_updated_at 
    BEFORE UPDATE ON tenants.user_deadline_insights 
    FOR EACH ROW EXECUTE FUNCTION tenants.update_updated_at_column();

-- Create function to clean up expired insights
CREATE OR REPLACE FUNCTION tenants.cleanup_expired_insights()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired deadline insights
    DELETE FROM tenants.deadline_insights 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Delete expired user deadline insights
    DELETE FROM tenants.user_deadline_insights 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Enable Row Level Security (RLS)
ALTER TABLE tenants.deadline_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenants.user_deadline_insights ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for deadline_insights
CREATE POLICY "Users can view their tenant's deadline insights" ON tenants.deadline_insights
    FOR SELECT USING (
        tenant_id IN (
            SELECT tenant_id FROM tenants.users 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Service role can manage all deadline insights" ON tenants.deadline_insights
    FOR ALL USING (auth.role() = 'service_role');

-- Create RLS policies for user_deadline_insights
CREATE POLICY "Users can view their own deadline insights" ON tenants.user_deadline_insights
    FOR SELECT USING (
        user_id = auth.uid() OR 
        tenant_id IN (
            SELECT tenant_id FROM tenants.users 
            WHERE id = auth.uid() AND role IN ('admin', 'superadmin')
        )
    );

CREATE POLICY "Service role can manage all user deadline insights" ON tenants.user_deadline_insights
    FOR ALL USING (auth.role() = 'service_role');

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON tenants.deadline_insights TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON tenants.user_deadline_insights TO authenticated;
GRANT USAGE ON SCHEMA tenants TO authenticated;

-- Grant service role full access
GRANT ALL ON tenants.deadline_insights TO service_role;
GRANT ALL ON tenants.user_deadline_insights TO service_role;

-- Add comments for documentation
COMMENT ON TABLE tenants.deadline_insights IS 'Stores deadline insights analysis results for tenants';
COMMENT ON TABLE tenants.user_deadline_insights IS 'Stores user-specific deadline insights for return scenarios';

COMMENT ON COLUMN tenants.deadline_insights.analysis_type IS 'Type of analysis: comprehensive, conflict_detection, risk_assessment, etc.';
COMMENT ON COLUMN tenants.deadline_insights.insights IS 'JSON data containing analysis results, recommendations, and metadata';
COMMENT ON COLUMN tenants.deadline_insights.expires_at IS 'When these insights expire and should be regenerated';

COMMENT ON COLUMN tenants.user_deadline_insights.analysis_type IS 'Type of user analysis: user_return, personalized, etc.';
COMMENT ON COLUMN tenants.user_deadline_insights.insights IS 'JSON data containing user-specific insights and recommendations';

-- Create a view for active insights (non-expired)
CREATE OR REPLACE VIEW tenants.active_deadline_insights AS
SELECT 
    id,
    tenant_id,
    analysis_type,
    insights,
    generated_at,
    expires_at,
    created_at,
    updated_at
FROM tenants.deadline_insights
WHERE expires_at > NOW()
ORDER BY generated_at DESC;

-- Create a view for active user insights
CREATE OR REPLACE VIEW tenants.active_user_deadline_insights AS
SELECT 
    id,
    user_id,
    tenant_id,
    analysis_type,
    insights,
    generated_at,
    expires_at,
    created_at,
    updated_at
FROM tenants.user_deadline_insights
WHERE expires_at > NOW()
ORDER BY generated_at DESC;

-- Grant access to views
GRANT SELECT ON tenants.active_deadline_insights TO authenticated;
GRANT SELECT ON tenants.active_user_deadline_insights TO authenticated;
GRANT ALL ON tenants.active_deadline_insights TO service_role;
GRANT ALL ON tenants.active_user_deadline_insights TO service_role;
